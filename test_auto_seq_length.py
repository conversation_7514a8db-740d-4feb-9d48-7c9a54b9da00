#!/usr/bin/env python3
"""
测试自动化序列长度设置功能
验证模型能够自动适应不同的序列长度
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import math

class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=1000):
        super().__init__()
        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(max_len, d_model)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        seq_len = x.size(1)
        if seq_len > self.pe.size(1):
            self._extend_pe(seq_len, x.device)
        return x + self.pe[:, :seq_len]
    
    def _extend_pe(self, new_max_len, device):
        """动态扩展位置编码"""
        d_model = self.pe.size(2)
        position = torch.arange(new_max_len, device=device).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2, device=device) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(new_max_len, d_model, device=device)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

class SimpleTransformerModel(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim, seq_length=None):
        super().__init__()
        
        self.seq_length = seq_length if seq_length is not None else 1000
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        self.pos_encoder = PositionalEncoding(hidden_dim, max_len=max(1000, self.seq_length))
        
        self.transformer_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=4,
            dim_feedforward=hidden_dim * 2,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(self.transformer_layer, num_layers=2)
        self.output_projection = nn.Linear(hidden_dim, output_dim)
        
    def forward(self, x):
        # x shape: [batch_size, seq_length]
        original_input = x.unsqueeze(-1)  # [batch_size, seq_length, 1]
        x = self.input_projection(original_input)  # [batch_size, seq_length, hidden_dim]
        x = self.pos_encoder(x)
        x = self.transformer(x)
        output = self.output_projection(x)
        return output.squeeze(-1)  # [batch_size, seq_length]

def load_excel_data(excel_path):
    """加载Excel数据"""
    print("正在加载Excel数据...")
    
    force_df = pd.read_excel(excel_path, sheet_name='Force_Data')
    accel_df = pd.read_excel(excel_path, sheet_name='Acceleration_Data')
    
    force_columns = [col for col in force_df.columns if col.startswith('2-1F-')]
    accel_columns = [col for col in accel_df.columns if col.startswith('2-1A1-')]
    
    matched_pairs = []
    for f_col in force_columns:
        event_num = f_col.split('-')[-1]
        a_col = f'2-1A1-{event_num}'
        if a_col in accel_columns:
            matched_pairs.append((f_col, a_col))
    
    if matched_pairs:
        force_data = force_df[[pair[0] for pair in matched_pairs]]
        accel_data = accel_df[[pair[1] for pair in matched_pairs]]
        return force_data, accel_data, matched_pairs
    else:
        raise ValueError("未找到匹配的F-A1信号对")

def test_different_seq_lengths():
    """测试不同序列长度的自动适应"""
    print("测试自动化序列长度设置...")
    
    # 加载数据
    excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
    F_Load, A_Res, matched_pairs = load_excel_data(excel_path)
    
    # 测试不同的数据范围
    test_ranges = [
        (0, 100),    # 100个时间点
        (0, 300),    # 300个时间点
        (0, 500),    # 500个时间点
        (100, 600),  # 500个时间点，不同起始位置
    ]
    
    for start_idx, end_idx in test_ranges:
        print(f"\n测试数据范围: {start_idx}:{end_idx}")
        
        # 检查数据范围
        data_length = len(F_Load)
        if end_idx > data_length:
            end_idx = data_length
            print(f"调整结束索引为: {end_idx}")
        
        # 提取数据
        F_D = F_Load.iloc[start_idx:end_idx, :]
        A_R = A_Res.iloc[start_idx:end_idx, :]
        
        # 转换为numpy并转置
        A_R_np = np.array(A_R).T
        F_D_np = np.array(F_D).T
        
        actual_seq_length = A_R_np.shape[1]
        num_samples = A_R_np.shape[0]
        
        print(f"实际序列长度: {actual_seq_length}")
        print(f"样本数量: {num_samples}")
        
        # 转换为PyTorch张量
        X_data = torch.tensor(A_R_np[:10], dtype=torch.float32)  # 只取前10个样本测试
        Y_data = torch.tensor(F_D_np[:10], dtype=torch.float32)
        
        # 创建模型（不指定序列长度，让其自动适应）
        model = SimpleTransformerModel(
            input_dim=1,
            hidden_dim=32,
            output_dim=1,
            seq_length=None  # 自动适应
        )
        
        print(f"模型初始位置编码长度: {model.pos_encoder.pe.size(1)}")
        
        # 测试前向传播
        try:
            with torch.no_grad():
                output = model(X_data)
                print(f"输入形状: {X_data.shape}")
                print(f"输出形状: {output.shape}")
                print(f"前向传播成功！")
                
                # 检查位置编码是否自动扩展
                if actual_seq_length > 1000:
                    print(f"位置编码已自动扩展到: {model.pos_encoder.pe.size(1)}")
                
        except Exception as e:
            print(f"前向传播失败: {e}")
            continue
        
        # 简单训练测试
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        print("开始简单训练测试...")
        for epoch in range(3):
            optimizer.zero_grad()
            pred = model(X_data)
            loss = criterion(pred, Y_data)
            loss.backward()
            optimizer.step()
            print(f"  Epoch {epoch+1}, Loss: {loss.item():.6f}")
        
        print(f"序列长度 {actual_seq_length} 测试完成！")
    
    print("\n✅ 所有序列长度测试完成！")

def test_model_parameters():
    """测试模型参数的自动计算"""
    print("\n测试模型参数自动计算...")
    
    # 模拟不同的数据形状
    test_shapes = [
        (100, 50),   # 100个时间点，50个样本
        (300, 80),   # 300个时间点，80个样本
        (500, 60),   # 500个时间点，60个样本
    ]
    
    for seq_len, num_samples in test_shapes:
        print(f"\n数据形状: ({seq_len}, {num_samples})")
        
        # 自动计算参数
        actual_seq_length = seq_len
        input_dim = 1
        hidden_dim = 64
        output_dim = 1
        batch_size = min(8, num_samples)  # 自动调整批次大小
        
        print(f"自动设置参数:")
        print(f"  序列长度: {actual_seq_length}")
        print(f"  隐藏层维度: {hidden_dim}")
        print(f"  批次大小: {batch_size}")
        
        # 创建模型
        model = SimpleTransformerModel(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            output_dim=output_dim,
            seq_length=actual_seq_length
        )
        
        # 测试数据
        test_input = torch.randn(batch_size, actual_seq_length)
        
        try:
            with torch.no_grad():
                output = model(test_input)
                print(f"  测试成功！输出形状: {output.shape}")
        except Exception as e:
            print(f"  测试失败: {e}")

if __name__ == "__main__":
    test_different_seq_lengths()
    test_model_parameters()
