# ==========================================
# 重要提示：请勿删除以下类型的标记行！
# - # <CELL_BOUNDARY>
# - # <CELL_TYPE:code> 或 # <CELL_TYPE:markdown>  
# - # <CELL_END>
# 这些标记用于重新转换为Jupyter notebook
# ==========================================

# 从 T_end_2 局部 copy 2-best.ipynb 转换而来
# 使用 notebook_converter_gui.py 工具转换

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from torch.utils.data import DataLoader, Dataset  # 确保导入Dataset
import math
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# ==================== 配置管理 ====================
CONFIG = {
    # 数据配置
    'data': {
        'excel_path': '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx',
        'cache_dir': './cache',  # 缓存目录
        'use_cache': True,       # 是否使用缓存
        'test_point': '2-2',     # 测点编号（可选：'2-1', '2-2', '2-3'等）
        'sensor_type': 'A1',     # 传感器类型（A1, A2, A3等）
        'start_idx': 0,
        'end_idx': 400,
        'threshold': 0.04,
        'train_ratio': 0.8,
        'random_seed': 500,
    },

    # 模型配置
    'model': {
        'input_dim': 1,
        'output_dim': 1,
        'hidden_dim': 64,  # 会根据序列长度自动调整
        'num_layers': 2,
        'window_configs': [(1,5), (2,3)],
        'max_pos_encoding': 1000,
    },

    # 训练配置
    'training': {
        'batch_size': 8,
        'num_epochs': 50,
        'learning_rate': 0.0005,
        'loss_alpha': 1.0,
        'loss_beta': 1.0,
    },

    # 输出配置
    'output': {
        'result_dir': './result',
        'model_dir': './models',
        'scaler_file': 'scaler_X.csv',
        'model_file': 'transformer_model_auto.pth',
    }
}

def load_test_point_data_with_cache(excel_path, test_point='2-1', sensor_type='A1', cache_dir='./cache'):
    """
    带缓存的测点数据加载函数
    支持不同测点和传感器类型的数据加载

    参数:
    excel_path: Excel文件路径
    test_point: 测点编号，如 '2-1', '2-2', '2-3' 等
    sensor_type: 传感器类型，如 'A1', 'A2', 'A3' 等
    cache_dir: 缓存目录

    返回:
    F_data: 力信号数据 (DataFrame)
    A_data: 加速度信号数据 (DataFrame)
    """
    import os

    # 创建缓存目录
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
        print(f"创建缓存目录: {cache_dir}")

    # 缓存文件路径（基于测点和传感器类型）
    cache_force_file = os.path.join(cache_dir, f'{test_point}_force_data.xlsx')
    cache_accel_file = os.path.join(cache_dir, f'{test_point}_{sensor_type}_accel_data.xlsx')

    # 检查缓存文件是否存在
    if os.path.exists(cache_force_file) and os.path.exists(cache_accel_file):
        print(f"发现{test_point}-{sensor_type}缓存文件，直接加载...")
        try:
            F_data = pd.read_excel(cache_force_file)
            A_data = pd.read_excel(cache_accel_file)
            print(f"从缓存加载 - 力信号: {F_data.shape}, 加速度信号: {A_data.shape}")
            return F_data, A_data
        except Exception as e:
            print(f"缓存文件读取失败: {e}")
            print("将重新生成缓存文件...")

    # 缓存文件不存在或读取失败，从原始文件提取
    print(f"从原始文件提取{test_point}-{sensor_type}相关数据...")

    # 读取原始数据
    print("正在读取Force_Data工作表...")
    force_df = pd.read_excel(excel_path, sheet_name='Force_Data')
    print("正在读取Acceleration_Data工作表...")
    accel_df = pd.read_excel(excel_path, sheet_name='Acceleration_Data')

    # 构造列名模式
    force_pattern = f'{test_point}F-'
    accel_pattern = f'{test_point}{sensor_type}-'

    # 提取相关列
    print(f"提取{test_point}-{sensor_type}相关列...")
    force_cols = [col for col in force_df.columns if col.startswith(force_pattern)]
    accel_cols = [col for col in accel_df.columns if col.startswith(accel_pattern)]

    print(f"找到力信号列: {len(force_cols)}个")
    print(f"找到加速度信号列: {len(accel_cols)}个")

    if len(force_cols) == 0:
        raise ValueError(f"未找到{test_point}的力信号数据（模式：{force_pattern}）")

    if len(accel_cols) == 0:
        raise ValueError(f"未找到{test_point}-{sensor_type}的加速度信号数据（模式：{accel_pattern}）")

    # 提取数据
    F_data = force_df[force_cols]
    A_data = accel_df[accel_cols]

    print(f"提取完成 - 力信号: {F_data.shape}, 加速度信号: {A_data.shape}")
    print(f"匹配的信号对数量: {min(len(force_cols), len(accel_cols))}")

    # 保存到缓存文件
    print("保存到缓存文件...")
    try:
        F_data.to_excel(cache_force_file, index=False)
        A_data.to_excel(cache_accel_file, index=False)
        print(f"缓存文件已保存:")
        print(f"  力信号: {cache_force_file}")
        print(f"  加速度信号: {cache_accel_file}")
    except Exception as e:
        print(f"缓存文件保存失败: {e}")

    return F_data, A_data

def clear_cache(cache_dir='./cache'):
    """清除缓存文件"""
    import os
    cache_files = [
        os.path.join(cache_dir, '2-1_force_data.xlsx'),
        os.path.join(cache_dir, '2-1_accel_data.xlsx')
    ]

    for cache_file in cache_files:
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print(f"已删除缓存文件: {cache_file}")

    if os.path.exists(cache_dir) and not os.listdir(cache_dir):
        os.rmdir(cache_dir)
        print(f"已删除空缓存目录: {cache_dir}")

# 加载数据（使用缓存机制）
F_Load, A_Res = load_test_point_data_with_cache(
    excel_path=CONFIG['data']['excel_path'],
    test_point=CONFIG['data']['test_point'],
    sensor_type=CONFIG['data']['sensor_type'],
    cache_dir=CONFIG['data']['cache_dir']
)

# 数据预处理：直接指定需要的数据范围
# 使用配置中的参数
start_idx = CONFIG['data']['start_idx']
end_idx = CONFIG['data']['end_idx']

# 检查数据范围
data_length = len(F_Load)
if end_idx > data_length:
    print(f"警告: 指定的结束索引 {end_idx} 超过数据长度 {data_length}，将调整为 {data_length}")
    end_idx = data_length
if start_idx < 0:
    start_idx = 0
if start_idx >= end_idx:
    raise ValueError(f"起始索引 {start_idx} 必须小于结束索引 {end_idx}")

F_D = F_Load.iloc[start_idx:end_idx, :]
A_R = A_Res.iloc[start_idx:end_idx, :]

print(f"数据范围: {start_idx}:{end_idx}")
print(f"力信号形状: {F_D.shape}, 加速度信号形状: {A_R.shape}")
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
A_R.shape
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def replace_abs_before(arr, threshold):
    """
    数据预处理函数：将阈值前的数据置零

    参数:
    arr: 输入数组
    threshold: 阈值

    返回:
    处理后的数组
    """
    num_samples = arr.shape[0]  # 自动获取样本数量
    for i in range(num_samples):
        abs_arr = np.abs(arr[i])
        first_index = np.argmax(abs_arr > threshold)
        arr[i][:first_index] = 0
    return arr

# 转换为numpy数组
A_R = np.array(A_R)
F_D = np.array(F_D)

# 使用配置中的阈值
threshold = CONFIG['data']['threshold']
train_datas_N = A_R
Y_datas = F_D

print(f"数据形状: {train_datas_N.shape}")
print(f"使用阈值: {threshold}")

# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
train_datas_N= train_datas_N.T
Y_datas= Y_datas.T
np.shape(train_datas_N)
train_datas = replace_abs_before(train_datas_N, threshold)

# 使用配置中的随机种子
seed = CONFIG['data']['random_seed']
np.random.seed(seed)
np.random.shuffle(train_datas)
np.random.seed(seed)
np.random.shuffle(Y_datas)

# 使用配置中的训练比例
total_samples = train_datas.shape[0]
train_ratio = CONFIG['data']['train_ratio']
train_size = int(total_samples * train_ratio)

print(f"总样本数: {total_samples}")
print(f"训练集大小: {train_size}")
print(f"测试集大小: {total_samples - train_size}")

# 划分数据
X_train = train_datas[:train_size, :]
X_test = train_datas[train_size:, :]
Y_train = Y_datas[:train_size, :]
Y_test = Y_datas[train_size:, :]

print(f"X_train形状: {X_train.shape}")
print(f"X_test形状: {X_test.shape}")
print(f"Y_train形状: {Y_train.shape}")
print(f"Y_test形状: {Y_test.shape}")
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# 将数据转为 PyTorch 张量（如果尚未是张量）
X_train = torch.tensor(X_train, dtype=torch.float32)
X_test = torch.tensor(X_test, dtype=torch.float32)
Y_train = torch.tensor(Y_train, dtype=torch.float32)  # 确保 Y 是二维列向量
Y_test = torch.tensor(Y_test, dtype=torch.float32)

# 对 X 数据进行标准化
X_mean = X_train.mean(dim=0)  # 按列计算均值
X_std = X_train.std(dim=0)    # 按列计算标准差
X_train = (X_train - X_mean) / X_std
X_test = (X_test - X_mean) / X_std  # 使用训练集的均值和标准差标准化测试集
# 对 Y 数据进行标准化
Y_mean = Y_train.mean(dim=0)
Y_std = Y_train.std(dim=0)
Y_train = (Y_train - Y_mean) / Y_std
Y_test = (Y_test - Y_mean) / Y_std  # 使用训练集的均值和标准差标准化测试集

# 如果需要返回均值和标准差（方便反归一化）
scaler_X = {'mean': X_mean, 'std': X_std}
print(scaler_X)
df = pd.DataFrame(scaler_X)

# 保存为CSV文件
df.to_csv(f"{CONFIG['output']['result_dir']}/{CONFIG['output']['scaler_file']}", index=False)

scaler_Y = {'mean': Y_mean, 'std': Y_std}
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
Y_train.shape
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=1000):  # 增加默认最大长度以适应更长序列
        super().__init__()
        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(max_len, d_model)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)

    def forward(self, x):
        # 自动适应输入序列长度
        seq_len = x.size(1)
        if seq_len > self.pe.size(1):
            # 如果输入序列长度超过预设最大长度，动态扩展位置编码
            self._extend_pe(seq_len, x.device)
        return x + self.pe[:, :seq_len]

    def _extend_pe(self, new_max_len, device):
        """动态扩展位置编码"""
        d_model = self.pe.size(2)
        position = torch.arange(new_max_len, device=device).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2, device=device) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(new_max_len, d_model, device=device)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        self.register_buffer('pe', pe)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
class MultiHeadLocalAttention(nn.Module):
    def __init__(self, d_model, num_heads=2, window_before=1, window_after=4):
        super().__init__()
        assert d_model % num_heads == 0, "d_model must be divisible by num_heads"
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        self.window_before = window_before
        self.window_after = window_after
        self.scale = math.sqrt(self.head_dim)
        
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        self.output_linear = nn.Linear(d_model, d_model)
        
    def forward(self, x):
        B, L, D = x.shape
        
        Q = self.q_linear(x).view(B, L, self.num_heads, self.head_dim)
        K = self.k_linear(x).view(B, L, self.num_heads, self.head_dim)
        V = self.v_linear(x).view(B, L, self.num_heads, self.head_dim)
        
        Q = Q.transpose(1, 2)
        K = K.transpose(1, 2)
        V = V.transpose(1, 2)
        
        attention_outputs = []
        for i in range(L):
            start_idx = max(0, i - self.window_before)  # 前向窗口
            end_idx = min(L, i + self.window_after + 1)  # 后向窗口
            
            q = Q[:, :, i:i+1]
            k = K[:, :, start_idx:end_idx]
            v = V[:, :, start_idx:end_idx]
            
            scores = torch.matmul(q, k.transpose(-2, -1)) / self.scale
            attn_weights = torch.softmax(scores, dim=-1)
            
            head_output = torch.matmul(attn_weights, v)
            attention_outputs.append(head_output)
        
        attention_output = torch.cat(attention_outputs, dim=2)
        attention_output = attention_output.transpose(1, 2).contiguous()
        attention_output = attention_output.view(B, L, self.d_model)
        
        output = self.output_linear(attention_output)
        return output


# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
A_R.shape[1] 
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
class SlidingWindowFeedForward(nn.Module):
    def __init__(self, hidden_dim, window_before, window_after):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.window_before = window_before
        self.window_after = window_after
        self.window_size = window_before + window_after + 1  # +1 for current position
        
        # 定义滑动窗口的前馈网络
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim * self.window_size, hidden_dim * 4),
            nn.PReLU(),
            nn.Dropout(0.05),
            nn.Linear(hidden_dim * 4, hidden_dim)
        )
    
    def forward(self, x):
        # x shape: [batch_size, seq_length, hidden_dim]
        B, L, D = x.shape
        output = torch.zeros_like(x)
        
        # 对每个位置使用滑动窗口计算
        for i in range(L):
            # 确定窗口范围
            start_idx = max(0, i - self.window_before)
            end_idx = min(L, i + self.window_after + 1)
            
            # 提取窗口数据
            window = x[:, start_idx:end_idx, :]
            # 如果窗口大小不足，进行填充
            if window.size(1) < self.window_size:
                padding_size = self.window_size - window.size(1)
                padding = torch.zeros(B, padding_size, D, device=x.device)
                if i < self.window_before:  # 序列开始处
                    window = torch.cat([padding, window], dim=1)
                else:  # 序列结束处
                    window = torch.cat([window, padding], dim=1)
            
            # 展平窗口数据
            window_flat = window.reshape(B, -1)
            # 计算该位置的输出
            pos_output = self.feed_forward(window_flat)
            output[:, i, :] = pos_output
            
        return output
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
class TransformerModel(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim, seq_length=None, num_layers=1,
                 window_configs=[(2,16), (2,16)]):  # 每层的(before, after)配置
        super().__init__()

        # 如果没有指定序列长度，使用一个较大的默认值，位置编码会自动适应
        self.seq_length = seq_length if seq_length is not None else 1000
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        # 位置编码会自动适应实际输入长度
        self.pos_encoder = PositionalEncoding(hidden_dim, max_len=max(1000, self.seq_length))
        
        # 创建两个不同配置的注意力层
        self.attention_layers = nn.ModuleList([
            MultiHeadLocalAttention(
                d_model=hidden_dim,
                num_heads=2,
                window_before=window_configs[i][0],
                window_after=window_configs[i][1]
            ) for i in range(num_layers)
        ])
        
        self.norm_layers = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])
        
        # 替换原来的 feedforward 为新的 SlidingWindowFeedForward
        # 在 TransformerModel 中使用时：
        self.sliding_feedforward = SlidingWindowFeedForward(hidden_dim, window_before=2, window_after=8)
        self.output_projection = nn.Linear(hidden_dim, output_dim)
        self.dropout = nn.Dropout(0.05)
        
    def forward(self, x):
        original_input = x.unsqueeze(-1)
        x = self.input_projection(original_input)
        x = self.pos_encoder(x)
        
        # 第一层注意力
        attention_output = self.attention_layers[0](x)
        x = x + self.dropout(attention_output)
        x = self.norm_layers[0](x)
        x = x + self.dropout(self.sliding_feedforward(x))
        
        # 第二层注意力
        attention_output = self.attention_layers[1](x)
        x = x + self.dropout(attention_output)
        x = self.norm_layers[1](x)
        
        x = self.sliding_feedforward(x)
        
        output = self.output_projection(x)
        return output.squeeze(-1)
# 打印训练数据的维度
print("X_train shape:", X_train.shape)
print("Y_train shape:", Y_train.shape)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
class CombinedLoss(nn.Module):
    def __init__(self, alpha=1.0, beta=1.0):
        super().__init__()
        self.alpha = alpha
        self.beta = beta

    def forward(self, pred, target):
        # MSE损失
        mse_loss = torch.mean((pred - target)**2)
        
        # 导数匹配损失   
        pred_grad = pred[:, 1:] - pred[:, :-1]
        target_grad = target[:, 1:] - target[:, :-1]
        gradient_loss = torch.mean((pred_grad - target_grad)**2)
        
        # 组合损失
        return self.alpha * mse_loss + self.beta * gradient_loss
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def auto_configure_params(A_R_shape):
    """根据数据形状自动调整配置参数"""
    seq_length = A_R_shape[0]  # 序列长度
    num_samples = A_R_shape[1]  # 样本数量

    # 根据序列长度自动调整隐藏层维度
    if seq_length < 200:
        auto_hidden_dim = 32
    elif seq_length < 500:
        auto_hidden_dim = 64
    else:
        auto_hidden_dim = 128

    # 根据样本数量自动调整批次大小
    auto_batch_size = min(CONFIG['training']['batch_size'], max(1, num_samples // 10))

    # 更新配置
    CONFIG['model']['seq_length'] = seq_length
    CONFIG['model']['num_samples'] = num_samples
    CONFIG['model']['hidden_dim'] = auto_hidden_dim
    CONFIG['training']['batch_size'] = auto_batch_size

    return CONFIG

# 自动配置参数
config = auto_configure_params(A_R.shape)

# 提取配置参数
batch_size = config['training']['batch_size']
num_epochs = config['training']['num_epochs']
input_dim = config['model']['input_dim']
hidden_dim = config['model']['hidden_dim']
output_dim = config['model']['output_dim']
seq_length = config['model']['seq_length']
learning_rate = config['training']['learning_rate']
window_configs = config['model']['window_configs']

# 打印配置信息
print("=" * 50)
print("自动配置的模型参数:")
print(f"数据形状: {A_R.shape}")
print(f"序列长度: {seq_length}")
print(f"样本数量: {config['model']['num_samples']}")
print(f"批次大小: {batch_size}")
print(f"隐藏层维度: {hidden_dim}")
print(f"训练轮数: {num_epochs}")
print(f"学习率: {learning_rate}")
print(f"注意力窗口配置: {window_configs}")
print("=" * 50)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# 创建模型实例（使用自动配置的参数）
model = TransformerModel(
    input_dim=input_dim,
    hidden_dim=hidden_dim,
    output_dim=output_dim,
    seq_length=seq_length,
    num_layers=2,
    window_configs=window_configs  # 使用配置中的窗口设置
)

# 定义损失函数和优化器（使用配置中的参数）
criterion = CombinedLoss(
    alpha=config['training']['loss_alpha'],
    beta=config['training']['loss_beta']
)
optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)

print(f"模型创建完成:")
print(f"  输入维度: {input_dim}")
print(f"  隐藏维度: {hidden_dim}")
print(f"  输出维度: {output_dim}")
print(f"  序列长度: {seq_length}")
print(f"  学习率: {learning_rate}")
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
train_dataset = torch.utils.data.TensorDataset(X_train, Y_train)
train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

# 在训练循环之前添加
print("Starting training...")
print(f"Total epochs: {num_epochs}")
print(f"Batch size: {batch_size}")
print(f"Training samples: {len(X_train)}")
print(f"Testing samples: {len(X_test)}")
print("=" * 50)

# 创建验证数据加载器
val_dataset = torch.utils.data.TensorDataset(X_test, Y_test)
val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# 用于记录最佳模型
best_val_loss = float('inf')
best_model_state = None
# 训练循环
for epoch in range(num_epochs):
    model.train()
    total_train_loss = 0
    batch_count = 0
    
    # 训练阶段
    for batch_X, batch_y in train_loader:
        optimizer.zero_grad()
        outputs = model(batch_X)
        loss = criterion(outputs, batch_y)
        loss.backward()
        optimizer.step()
        
        total_train_loss += loss.item()
        batch_count += 1
        
        # 打印每个batch的���度
        if batch_count % 10 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}] - Batch [{batch_count}/{len(train_loader)}] - '
                  f'Loss: {loss.item():.6f}')
    
    # 计算平均训练损失
    avg_train_loss = total_train_loss / len(train_loader)
    
    # 验证阶段
    model.eval()
    total_val_loss = 0
    with torch.no_grad():
        for batch_X, batch_y in val_loader:
            val_outputs = model(batch_X)
            val_loss = criterion(val_outputs, batch_y)
            total_val_loss += val_loss.item()
    
    avg_val_loss = total_val_loss / len(val_loader)
    
    # 保存最佳模型
    if avg_val_loss < best_val_loss:
        best_val_loss = avg_val_loss
        best_model_state = model.state_dict()
        print(f"New best model saved! Validation Loss: {best_val_loss:.6f}")
            
    # 打印每个epoch的结果
    print(f"Epoch [{epoch+1}/{num_epochs}]")
    print(f"Average Training Loss: {avg_train_loss:.6f}")
    print(f"Average Validation Loss: {avg_val_loss:.6f}")
    print("-" * 50)
    
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# 训练结束后的评估
print("Training completed!")
print("=" * 50)
##print(f"Best validation loss: {best_val_loss:.6f}")
# 如果有预训练模型，可以加载
# checkpoint = torch.load('./models/transformer_model-局部-best-X.pth')# 替换为你的模型文件路径
# model.load_state_dict(checkpoint['model_state_dict'])
# 加载最佳模型
# model.load_state_dict(best_model_state)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
X_test.shape
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# 最终测试评估
model.eval()
with torch.no_grad():
    test_predictions = model(X_test)
    #final_test_loss = criterion(test_predictions, Y_test)
    
    # 计算R²分数
    #y_mean = torch.mean(Y_test)
    #ss_tot = torch.sum((Y_test - y_mean) ** 2)
    #ss_res = torch.sum((Y_test - test_predictions) ** 2)
    #r2 = 1 - (ss_res / ss_tot)
    
    #print(f"Final Test Loss: {final_test_loss.item():.6f}")
    #print(f"R² Score: {r2.item():.4f}")
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# 检查数据类型并相应处理
# 对于 test_predictions
if torch.is_tensor(test_predictions):
    test_predictions = test_predictions.detach().numpy()
X_test_original = X_test.detach().numpy() *scaler_X['std'].numpy() + scaler_X['mean'].numpy()
# 对于 Y_test
if torch.is_tensor(Y_test):
    Y_test_original = Y_test.detach().numpy() * scaler_Y['std'].numpy() + scaler_Y['mean'].numpy()
else:
    Y_test_original = Y_test * scaler_Y['std'].numpy() + scaler_Y['mean'].numpy()

# 对于 test_predictions 的反归一化
test_predictions_2 = test_predictions * scaler_Y['std'].numpy() + scaler_Y['mean'].numpy()
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
Y_test_original.shape
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# 保存模型
'''A_Rtorch.save({
    'epoch': num_epochs,
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': optimizer.state_dict(),
    'best_val_loss': best_val_loss,
    'scaler_X': scaler_X,
    'scaler_Y': scaler_Y
}, '/workspace/X-DATA/transformer_model-局部-best-X-xiao80.pth')

print("Model saved successfully!")'''
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
#Y_prediction=predictions
#Y_prediction = scaler_Y.inverse_transform(Y_prediction)
#Y_test=scaler_Y.inverse_transform(Y_test)
Y_prediction =test_predictions_2 
Y_test_2 = Y_test_original
X_test_2 = X_test_original
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def plot_rmse_with_trimmed_mean(Y_prediction, Y_test, n_trim=0):
    """
    计算并绘制RMSE，可以选择排除最大的n个值后计算平均值
    参数:
    Y_prediction: 预测值
    Y_test: 真实值
    n_trim: 要排除的最大值的数量
    
    返回:
    trimmed_mean: 排除最大值后的平均RMSE
    """
    # 计算每个样本的RMSE
    EVE_RMSE = np.sqrt(np.mean((Y_prediction - Y_test)**2, axis=1))
    
    # 如果n_trim > 0，排除最大的n个值
    if n_trim > 0:
        # 获取排序后的索引
        sorted_indices = np.argsort(EVE_RMSE)
        # 排除最大的n个值
        trimmed_indices = sorted_indices[:-n_trim]
        trimmed_RMSE = EVE_RMSE[trimmed_indices]
        trimmed_mean = np.mean(trimmed_RMSE)
    else:
        trimmed_mean = np.mean(EVE_RMSE)

    # 绘图
    plt.figure(figsize=(10, 6))
    plt.plot(range(len(EVE_RMSE)), EVE_RMSE, 'b-', label='Individual RMSE')
    plt.axhline(y=trimmed_mean, color='r', linestyle='--', 
                label=f'Trimmed Average RMSE (excluding top {n_trim}): {trimmed_mean:.6f}')
    
    plt.xlabel('Sample Index')
    plt.ylabel('RMSE')
    plt.title(f'RMSE for Each Sample (Excluding top {n_trim} values)')
    plt.legend()
    plt.grid(True)
    plt.show()
    
    return trimmed_mean

trimmed_rmse = plot_rmse_with_trimmed_mean(Y_prediction, Y_test_2, n_trim=0)
print(f"Trimmed Average RMSE: {trimmed_rmse:.6f}")
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def plot_re_samples_analysis(Y_prediction, Y_test, X_test, num_samples=1,
                            output_path="./result/analysis_data.csv",
                            random_seed=52):
    """
    完整可视化分析函数
    参数：
    - X_test: 输入特征数据 (n_samples, n_features)
    - 其他参数保持原功能
    """
    # 数据校验
    assert Y_prediction.shape == Y_test.shape, "预测值与真实值维度不一致"
    assert X_test.shape[0] == Y_test.shape[0], "输入输出样本数不匹配"
    
    total_samples = Y_test.shape[0]
    seq_length = Y_test.shape[1]
    
    # 计算相对误差
    numerator = np.sqrt(np.sum((Y_prediction - Y_test)**2, axis=1))
    denominator = np.sqrt(np.sum(Y_test**2, axis=1))
    denominator = np.where(denominator == 0, 1e-10, denominator)
    all_re = (numerator / denominator) * 100
    
    # 样本选择逻辑
    worst_indices = np.argsort(all_re)[-num_samples:][::-1]
    if random_seed is not None:
        np.random.seed(random_seed)
    random_idx = np.random.randint(total_samples)
    selected_indices = list(worst_indices) + [random_idx]
    
    # 准备数据框
    final_df = pd.DataFrame({'time': np.arange(seq_length)})
    
    # 可视化布局
    ncols = 2
    nrows = int(np.ceil(len(selected_indices)/ncols))
    fig, axes = plt.subplots(nrows, ncols, figsize=(15, 4*nrows))
    axes = axes.ravel()
    
    # 遍历所有选定样本
    for plot_idx, sample_idx in enumerate(selected_indices):
        ax = axes[plot_idx]
        y_true = Y_test[sample_idx]
        y_pred = Y_prediction[sample_idx]
        x_test = X_test[sample_idx]
        
        # --- 核心绘图逻辑开始 ---
        # 绘制主曲线
        ax.plot(final_df['time'], y_true, 'b-', linewidth=1.5, label='True', alpha=0.8)
        ax.plot(final_df['time'], y_pred, 'r--', linewidth=1.5, label='Pred', alpha=0.8)
        
        # 标记峰值点
        peak_time = np.argmax(np.abs(y_true))
        true_peak = y_true[peak_time]
        pred_peak = y_pred[peak_time]
        ax.scatter(peak_time, true_peak, color='blue', marker='*', s=120, edgecolor='black')
        ax.scatter(peak_time, pred_peak, color='red', marker='*', s=120, edgecolor='black')
        
        # 标注文本
        peak_error = abs((pred_peak - true_peak)/true_peak)*100 if true_peak !=0 else 0
        text_str = f'Peak error: {peak_error:.1f}%\nRE: {all_re[sample_idx]:.1f}%'
        ax.text(0.05, 0.95, text_str, transform=ax.transAxes, 
               verticalalignment='top', bbox={'boxstyle':'round', 'alpha':0.2})
        
        # 样式设置
        ax.set_title(f'Sample #{sample_idx}' + (' [RANDOM]' if sample_idx == random_idx else ''))
        ax.set_xlabel('Time step')
        ax.set_ylabel('Value')
        ax.legend(loc='upper right')
        # --- 核心绘图逻辑结束 ---
        
        # 添加数据到DataFrame
        final_df[f'true_{sample_idx}'] = y_true
        final_df[f'pred_{sample_idx}'] = y_pred
        final_df[f'x_test_{sample_idx}'] = x_test
    
    # 隐藏多余子图
    for ax in axes[len(selected_indices):]:
        ax.axis('off')
        
    plt.tight_layout()
    plt.show()
    
    # 数据导出
    final_df.to_csv(output_path, index=False)
    print(f"分析结果已保存至：{output_path}")
    return final_df

# 使用示例
analysis_data = plot_re_samples_analysis(
    Y_prediction,
    Y_test_2,
    X_test_2,
    num_samples=1,
    random_seed=52
)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
Y_prediction=np.array(Y_prediction)
Y_test_2=np.array(Y_test_2)
def plot_worst_predictions(Y_prediction, Y_test, n_worst=5):
    """
    绘制RMSE最大的n_worst个样本的预测结果对比
    
    参数:
    Y_prediction: 预测值
    Y_test: 真实值
    n_worst: 要展示的最差结果数量
    """
    # 计算每个样本的RMSE
    EVE_RMSE = np.sqrt(np.mean((Y_prediction - Y_test)**2, axis=1))
    
    # 获取RMSE最大的n_worst个样本的索引
    worst_indices = np.argsort(EVE_RMSE)[-n_worst:][::-1]  # 降序排列
    
    # 创建子图
    fig, axes = plt.subplots(n_worst, 1, figsize=(12, 4*n_worst))
    if n_worst == 1:
        axes = [axes]
    
    # 绘制每个最差样本的对比图
    for i, idx in enumerate(worst_indices):
        axes[i].plot(Y_test[idx], 'b-', label='True Value', linewidth=2)
        axes[i].plot(Y_prediction[idx], 'r--', label='Prediction', linewidth=2)
        axes[i].set_title(f'Sample {idx} (RMSE: {EVE_RMSE[idx]:.6f})')
        axes[i].set_xlabel('Time Step')
        axes[i].set_ylabel('Value')
        axes[i].legend()
        axes[i].grid(True)
    
    plt.tight_layout()
    plt.show()
    
    # 打印最差样本的RMSE值
    print("\nWorst Predictions RMSE Values:")
    for i, idx in enumerate(worst_indices):
        print(f"Sample {idx}: RMSE = {EVE_RMSE[idx]:.6f}")
    
    # 计算并打印平均RMSE
    mean_rmse = np.mean(EVE_RMSE)
    print(f"\nAverage RMSE across all samples: {mean_rmse:.6f}")

# 使用示例：
# 首先调用原来的函数
#trimmed_mse = plot_mse_with_trimmed_mean(Y_prediction, Y_test, n_trim=0)
#print(f"Trimmed Average MSE: {trimmed_mse:.6f}")

# 然后调用新函数展示最差的预测结果
plot_worst_predictions(Y_prediction, Y_test_2, n_worst=5)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def analyze_peak_errors(Y_test, Y_prediction):
    """
    分析99组数据的峰值误差并生成统计图表
    
    参数:
    Y_test: shape=(99, 300)
    Y_prediction: shape=(99, 300)
    """
    # 确保输入数据格式正确
    Y_test = np.array(Y_test)
    Y_prediction = np.array(Y_prediction)
    
    # 如果是torch张量,转换为numpy
    if torch.is_tensor(Y_test):
        Y_test = Y_test.detach().cpu().numpy()
    if torch.is_tensor(Y_prediction):
        Y_prediction = Y_prediction.detach().cpu().numpy()
    
    peak_errors = []
    true_peaks = []
    pred_peaks = []
    peak_positions = []
    b=0
    # 计算每个样本的峰值误差
    for i in range(len(Y_test)):
        # 找到真实数据绝对值最大值的位置
        abs_peak_idx = np.argmax(np.abs(Y_test[i]))
        
        # 获取该位置的真实值和预测值
        true_peak = Y_test[i][abs_peak_idx]
        pred_peak = Y_prediction[i][abs_peak_idx]
        
        # 计算相对误差 (pred - true) / true * 100%
        if true_peak != 0:
            relative_error = abs((pred_peak - true_peak) / true_peak * 100)
        else:
            relative_error = 0
        if relative_error >10:
            b=b+1

            
        peak_errors.append(relative_error)
        true_peaks.append(true_peak)
        pred_peaks.append(pred_peak)
        peak_positions.append(abs_peak_idx)
    
    peak_errors = np.array(peak_errors)
    df = pd.DataFrame(peak_errors, columns=['Peak_Errors'])
    
    # 保存到CSV文件
    df.to_csv('./result/X_peak_errors.csv', index=False)
    
    # 计算统计指标
    stats = {
        'max_error': np.max(peak_errors),
        'min_error': np.min(peak_errors),
        'mean_error': np.mean(peak_errors),
        'median_error': np.median(peak_errors),
        'std_error': np.std(peak_errors)
    }
    
    # 创建可视化
    plt.figure(figsize=(15, 10))
    
    # 1. 峰值误差分布直方图
    plt.subplot(221)
    plt.hist(peak_errors, bins=20, color='blue', alpha=0.7)
    plt.axvline(stats['mean_error'], color='r', linestyle='--', label='Mean')
    plt.axvline(stats['median_error'], color='g', linestyle='--', label='Median')
    plt.title('Distribution of Peak Errors')
    plt.xlabel('Peak Error (%)')
    plt.ylabel('Count')
    plt.legend()
    
    # 2. 峰值误差箱型图
    plt.subplot(222)
    plt.boxplot(peak_errors)
    plt.title('Boxplot of Peak Errors')
    plt.ylabel('Peak Error (%)')
    
    # 3. 序号-峰值误差散点图
    plt.subplot(223)
    plt.scatter(range(len(peak_errors)), peak_errors, alpha=0.6)
    plt.axhline(stats['mean_error'], color='r', linestyle='--', label='Mean')
    plt.axhline(stats['median_error'], color='g', linestyle='--', label='Median')
    plt.title('Peak Errors by Sample Index')
    plt.xlabel('Sample Index')
    plt.ylabel('Peak Error (%)')
    plt.legend()
    
    # 4. 真实峰值vs预测峰值散点图
    plt.subplot(224)
    plt.scatter(true_peaks, pred_peaks, alpha=0.6)
    plt.plot([min(true_peaks), max(true_peaks)], [min(true_peaks), max(true_peaks)], 'r--')
    plt.title('True vs Predicted Peak Values')
    plt.xlabel('True Peak')
    plt.ylabel('Predicted Peak')
    
    plt.tight_layout()
    plt.show()
    
    # 打印统计结果
    print("\n=== 峰值误差统计结果 ===")
    print(f"最大峰值误差: {stats['max_error']:.2f}%")
    print(f"最小峰值误差: {stats['min_error']:.2f}%")
    print(f"平均峰值误差: {stats['mean_error']:.2f}%")
    print(f"中位峰值误差: {stats['median_error']:.2f}%")
    print(f"峰值误差标准差: {stats['std_error']:.2f}%")
    print(b)
    return stats, peak_errors

# 使用示例：
stats, peak_errors = analyze_peak_errors(Y_test_2, Y_prediction)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def plot_worst_peak_samples(Y_prediction, Y_test, num_samples=10):
    """
    绘制峰值误差最大的前N个样本的对比曲线，显示RMSE和相对峰值误差
    """
    total_samples = Y_test.shape[0]
    
    # 计算RMSE
    all_rmse = np.sqrt(np.mean((Y_prediction - Y_test)**2, axis=1))
    avg_rmse = np.mean(all_rmse)
    
    # 计算相对峰值误差
    def calculate_peak_relative_error(y_true, y_pred):
        abs_peak_idx = np.argmax(np.abs(y_true))
        true_peak = y_true[abs_peak_idx]
        pred_peak = y_pred[abs_peak_idx]
        
        if true_peak != 0:
            relative_error = abs((pred_peak - true_peak) / true_peak * 100)  # 使用绝对值
        else:
            relative_error = 0
            
        return relative_error, true_peak, pred_peak, abs_peak_idx
    
    # 计算所有样本的峰值误差
    peak_errors = []
    for i in range(total_samples):
        error, _, _, _ = calculate_peak_relative_error(Y_test[i], Y_prediction[i])
        peak_errors.append(error)
    
    # 获取峰值误差最大的前N个样本的索引
    worst_indices = np.argsort(peak_errors)[-num_samples:][::-1]
    
    # 创建图表
    fig, axes = plt.subplots(5, 2, figsize=(15, 20))
    axes = axes.ravel()
    
    for idx, sample_idx in enumerate(worst_indices):
        ax = axes[idx]
        
        # 计算当前样本的误差指标
        sample_rmse = all_rmse[sample_idx]
        peak_error, true_peak, pred_peak, peak_idx = calculate_peak_relative_error(
            Y_test[sample_idx], Y_prediction[sample_idx]
        )
        
        # 绘制曲线
        time_points = np.arange(Y_test.shape[1])
        ax.plot(time_points, Y_test[sample_idx], 'b-', label='Real', alpha=0.7)
        ax.plot(time_points, Y_prediction[sample_idx], 'r--', label='Predicted', alpha=0.7)
        
        # 标记峰值点
        ax.scatter(peak_idx, true_peak, color='blue', s=100, marker='*', label='True Peak')
        ax.scatter(peak_idx, pred_peak, color='red', s=100, marker='*', label='Pred Peak')
        
        # 添加标题和标签
        ax.set_title(f'Worst Sample #{idx+1} (Index: {sample_idx})\n'
                    f'RMSE: {sample_rmse:.6f}\n'
                    f'Peak Error: {peak_error:.2f}%\n'
                    f'True Peak: {true_peak:.4f}\n'
                    f'Pred Peak: {pred_peak:.4f}\n'
                    f'Peak Position: {peak_idx}')
        ax.set_xlabel('Time Points')
        ax.set_ylabel('Value')
        ax.grid(True)
        ax.legend()
    
    avg_peak_error = np.mean(peak_errors)
    plt.suptitle(f'Worst {num_samples} Samples by Peak Error\n'
                f'Average RMSE: {avg_rmse:.6f}\n'
                f'Average Peak Error: {avg_peak_error:.2f}%', 
                fontsize=16, y=1.02)
    plt.tight_layout()
    plt.show()
    
    # 打印详细误差信息
    print(f"\nWorst {num_samples} samples and their error metrics:")
    for i, sample_idx in enumerate(worst_indices):
        print(f"Sample {sample_idx} (Rank {i+1}):")
        print(f"  RMSE = {all_rmse[sample_idx]:.6f}")
        print(f"  Peak Error = {peak_errors[sample_idx]:.2f}%")
    
    print(f"\nAverage metrics across all samples:")
    print(f"  RMSE: {avg_rmse:.6f}")
    print(f"  Peak Error: {avg_peak_error:.2f}%")

# 使用示例：
plot_worst_peak_samples(Y_prediction, Y_test_2, num_samples=10)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def plot_random_samples(Y_prediction, Y_test, num_samples=10):
    """
    随机选择并绘制预测值和真实值的对比曲线，显示RMSE和相对峰值误差
    """
    total_samples = Y_test.shape[0]
    np.random.seed(42)#42
    random_indices = np.random.choice(total_samples, num_samples, replace=False)
    
    fig, axes = plt.subplots(5, 2, figsize=(15, 20))
    axes = axes.ravel()
    
    # 计算RMSE
    all_rmse = np.sqrt(np.mean((Y_prediction - Y_test)**2, axis=1))
    avg_rmse = np.mean(all_rmse)
    
    # 计算相对峰值误差
    def calculate_peak_relative_error(y_true, y_pred):
        # 找到真实数据绝对值最大值的位置
        abs_peak_idx = np.argmax(np.abs(y_true))
        
        # 获取该位置的真实值和预测值
        true_peak = y_true[abs_peak_idx]
        pred_peak = y_pred[abs_peak_idx]
        
        # 计算相对误差 (pred - true) / true * 100%
        if true_peak != 0:
            relative_error = (pred_peak - true_peak) / true_peak * 100
        else:
            relative_error = 0
            
        return relative_error, true_peak, pred_peak, abs_peak_idx
    
    # 存储所有样本的峰值误差
    peak_errors = []
    
    for idx, sample_idx in enumerate(random_indices):
        ax = axes[idx]
        
        # 计算当前样本的误差指标
        sample_rmse = all_rmse[sample_idx]
        peak_error, true_peak, pred_peak, peak_idx = calculate_peak_relative_error(
            Y_test[sample_idx], Y_prediction[sample_idx]
        )
        peak_errors.append(peak_error)
        
        # 绘制曲线
        time_points = np.arange(Y_test.shape[1])
        ax.plot(time_points, Y_test[sample_idx], 'b-', label='Real', alpha=0.7)
        ax.plot(time_points, Y_prediction[sample_idx], 'r--', label='Predicted', alpha=0.7)
        
        # 标记峰值点
        ax.scatter(peak_idx, true_peak, color='blue', s=100, marker='*', label='True Peak')
        ax.scatter(peak_idx, pred_peak, color='red', s=100, marker='*', label='Pred Peak')
        
        # 添加标题和标签
        ax.set_title(f'Sample {sample_idx}\n'
                    f'RMSE: {sample_rmse:.6f}\n'
                    f'Peak Error: {peak_error:.2f}%\n'
                    f'True Peak: {true_peak:.4f}\n'
                    f'Pred Peak: {pred_peak:.4f}\n'
                    f'Peak Position: {peak_idx}')
        ax.set_xlabel('Time Points')
        ax.set_ylabel('Value')
        ax.grid(True)
        ax.legend()
    
    avg_peak_error = np.mean(peak_errors)
    plt.suptitle(f'Random Samples Comparison\n'
                f'Average RMSE: {avg_rmse:.6f}\n'
                f'Average Peak Error: {avg_peak_error:.2f}%', 
                fontsize=16, y=1.02)
    plt.tight_layout()
    plt.show()
    
    # 打印详细误差信息
    print("\nSelected samples and their error metrics:")
    for i, sample_idx in enumerate(sorted(random_indices)):
        print(f"Sample {sample_idx}:")
        print(f"  RMSE = {all_rmse[sample_idx]:.6f}")
        print(f"  Peak Error = {peak_errors[i]:.2f}%")
    
    print(f"\nAverage metrics across all samples:")
    print(f"  RMSE: {avg_rmse:.6f}")
    print(f"  Peak Error: {avg_peak_error:.2f}%")

# 使用示例：
plot_random_samples(Y_prediction, Y_test_2, num_samples=10)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def analyze_similarity_stats(Y_test, Y_prediction):
    """
    分析数据的R²相关系数并生成统计图表
    
    参数:
    Y_test: 实际值
    Y_prediction: 预测值
    """
    # 确保输入数据格式正确
    Y_test = np.array(Y_test)
    Y_prediction = np.array(Y_prediction)
    
    # 如果是torch张量，转换为numpy
    if torch.is_tensor(Y_test):
        Y_test = Y_test.detach().cpu().numpy()
    if torch.is_tensor(Y_prediction):
        Y_prediction = Y_prediction.detach().cpu().numpy()
    
    r2_scores = []
    
    for i in range(len(Y_test)):
        # 确保数据是一维的
        y_true = np.asarray(Y_test[i], dtype=np.float64)
        y_pred = np.asarray(Y_prediction[i], dtype=np.float64)
        
        # 计算R²
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        ss_res = np.sum((y_true - y_pred) ** 2)
        r2 = 1 - (ss_res / ss_tot)
        r2_scores.append(r2)
    
    r2_scores = np.array(r2_scores)
    df = pd.DataFrame(r2_scores, columns=['X_R_2'])
    
    # 保存到CSV文件
    df.to_csv('./result/X_R_2.csv', index=False)
    
    
    # 计算统计指标
    stats = {
        'max_r2': np.max(r2_scores),
        'min_r2': np.min(r2_scores),
        'mean_r2': np.mean(r2_scores),
        'median_r2': np.median(r2_scores),
        'std_r2': np.std(r2_scores)
    }
    
    # 创建可视化
    plt.figure(figsize=(15, 10))
    
    # 1. R²分布直方图
    plt.subplot(221)
    plt.hist(r2_scores, bins=20, color='blue', alpha=0.7)
    plt.axvline(stats['mean_r2'], color='r', linestyle='--', label='Mean')
    plt.axvline(stats['median_r2'], color='g', linestyle='--', label='Median')
    plt.title('Distribution of R² Scores')
    plt.xlabel('R² Score')
    plt.ylabel('Count')
    plt.legend()
    
    # 2. R²箱型图
    plt.subplot(222)
    plt.boxplot(r2_scores)
    plt.title('Boxplot of R² Scores')
    plt.ylabel('R² Score')
    
    # 3. 序号-R²散点图
    plt.subplot(223)
    plt.scatter(range(len(r2_scores)), r2_scores, alpha=0.6)
    plt.axhline(stats['mean_r2'], color='r', linestyle='--', label='Mean')
    plt.axhline(stats['median_r2'], color='g', linestyle='--', label='Median')
    plt.title('R² Scores by Sample Index')
    plt.xlabel('Sample Index')
    plt.ylabel('R² Score')
    plt.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 打印统计结果
    print("\n=== R²统计结果 ===")
    print(f"最大R²: {stats['max_r2']:.4f}")
    print(f"最小R²: {stats['min_r2']:.4f}")
    print(f"平均R²: {stats['mean_r2']:.4f}")
    print(f"中位R²: {stats['median_r2']:.4f}")
    print(f"R²标准差: {stats['std_r2']:.4f}")
    
    return stats, r2_scores

# 使用示例：
stats, r2_scores = analyze_similarity_stats(Y_test_2, Y_prediction)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
def analyze_peak_errors(Y_test, Y_prediction):
    """
    计算每个预测曲线与真实曲线的相对误差(RE)并进行可视化分析
    
    参数:
    Y_test: shape=(99, 300)
    Y_prediction: shape=(99, 300)
    
    返回:
    relative_errors: 每条曲线的相对误差列表
    """
    # 确保输入数据格式正确
    Y_test = np.array(Y_test)
    Y_prediction = np.array(Y_prediction)
    
    # 如果是torch张量,转换为numpy
    if torch.is_tensor(Y_test):
        Y_test = Y_test.detach().cpu().numpy()
    if torch.is_tensor(Y_prediction):
        Y_prediction = Y_prediction.detach().cpu().numpy()
    
    relative_errors = []
    
    # 计算每个样本的相对误差RE
    for i in range(len(Y_test)):
        # 计算分子：预测值与真实值差值的平方和的平方根
        numerator = np.sqrt(np.sum((Y_prediction[i] - Y_test[i])**2))
        
        # 计算分母：真实值的平方和的平方根
        denominator = np.sqrt(np.sum(Y_test[i]**2))
        
        # 计算相对误差RE (%)
        if denominator != 0:
            re = (numerator / denominator) * 100
        else:
            re = 0
            
        relative_errors.append(re)
    
    relative_errors = np.array(relative_errors)
    
    # 将relative_errors输出到CSV文件

    df = pd.DataFrame(relative_errors, columns=['Relative_Errors'])
    df.to_csv('./result/X-relative_errors.csv', index=False)
    
    # 添加可视化分析
    plt.figure(figsize=(12, 8))
    
    # 1. 绘制RE分布直方图
    plt.subplot(221)
    plt.hist(relative_errors, bins=20, color='skyblue', edgecolor='black')
    plt.title('RE分布直方图')
    plt.xlabel('相对误差 (%)')
    plt.ylabel('频数')
    
    # 2. 绘制RE箱线图
    plt.subplot(222)
    plt.boxplot(relative_errors, patch_artist=True)
    plt.title('RE箱线图')
    plt.ylabel('相对误差 (%)')
    
    # 3. 绘制RE累积分布图
    plt.subplot(223)
    sorted_errors = np.sort(relative_errors)
    cumulative = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)
    plt.plot(sorted_errors, cumulative, 'b-')
    plt.title('RE累积分布图')
    plt.xlabel('相对误差 (%)')
    plt.ylabel('累积概率')
    
    # 4. 计算并显示统计指标
    plt.subplot(224)
    plt.axis('off')
    stats_text = f'''统计指标:
    平均值: {np.mean(relative_errors):.2f}%
    中位数: {np.median(relative_errors):.2f}%
    标准差: {np.std(relative_errors):.2f}%
    最小值: {np.min(relative_errors):.2f}%
    最大值: {np.max(relative_errors):.2f}%
    '''
    plt.text(0.1, 0.5, stats_text, fontsize=10)
    # 调整子图布局
    plt.tight_layout()
    print("=== RE统计结果 ===")
    print(f"最大RE: {np.max(relative_errors):.4f}%")
    print(f"最小RE: {np.min(relative_errors):.4f}%")
    print(f"平均RE: {np.mean(relative_errors):.4f}%")
    print(f"中位RE: {np.median(relative_errors):.4f}%")
    print(f"RE标准差: {np.std(relative_errors):.4f}")
    
    return relative_errors
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
analyze_peak_errors(Y_test_2, Y_prediction)
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
# <CELL_END>

