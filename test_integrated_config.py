#!/usr/bin/env python3
"""
测试集成配置管理的代码
验证统一配置管理是否正常工作
"""

import pandas as pd
import numpy as np

# 测试配置
TEST_CONFIG = {
    # 数据配置
    'data': {
        'excel_path': '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx',
        'start_idx': 0,
        'end_idx': 300,  # 测试用较小范围
        'threshold': 0.04,
        'train_ratio': 0.8,
        'random_seed': 500,
    },
    
    # 模型配置
    'model': {
        'input_dim': 1,
        'output_dim': 1,
        'hidden_dim': 64,
        'num_layers': 2,
        'window_configs': [(1,5), (2,3)],
        'max_pos_encoding': 1000,
    },
    
    # 训练配置
    'training': {
        'batch_size': 4,  # 测试用较小批次
        'num_epochs': 3,  # 测试用较少轮数
        'learning_rate': 0.001,
        'loss_alpha': 1.0,
        'loss_beta': 1.0,
    },
    
    # 输出配置
    'output': {
        'result_dir': './result',
        'model_dir': './models',
        'scaler_file': 'test_scaler_X.csv',
        'model_file': 'test_transformer_model.pth',
    }
}

def load_2_1_data(excel_path):
    """简化的2-1数据加载函数"""
    print("加载2-1相关数据...")
    
    # 读取数据
    force_df = pd.read_excel(excel_path, sheet_name='Force_Data')
    accel_df = pd.read_excel(excel_path, sheet_name='Acceleration_Data')
    
    # 直接获取2-1相关列
    force_cols = [col for col in force_df.columns if col.startswith('2-1F-')]
    accel_cols = [col for col in accel_df.columns if col.startswith('2-1A1-')]
    
    # 提取数据
    F_data = force_df[force_cols]
    A_data = accel_df[accel_cols]
    
    print(f"力信号: {F_data.shape}, 加速度信号: {A_data.shape}")
    print(f"匹配的信号对数量: {min(len(force_cols), len(accel_cols))}")
    
    return F_data, A_data

def replace_abs_before(arr, threshold):
    """数据预处理函数"""
    num_samples = arr.shape[0]
    print(f"处理 {num_samples} 个样本...")
    for i in range(num_samples):
        abs_arr = np.abs(arr[i])
        first_index = np.argmax(abs_arr > threshold)
        arr[i][:first_index] = 0
    return arr

def auto_configure_params(A_R_shape, config):
    """根据数据形状自动调整配置参数"""
    seq_length = A_R_shape[0]
    num_samples = A_R_shape[1]
    
    # 根据序列长度自动调整隐藏层维度
    if seq_length < 200:
        auto_hidden_dim = 32
    elif seq_length < 500:
        auto_hidden_dim = 64
    else:
        auto_hidden_dim = 128
    
    # 根据样本数量自动调整批次大小
    auto_batch_size = min(config['training']['batch_size'], max(1, num_samples // 10))
    
    # 更新配置
    config['model']['seq_length'] = seq_length
    config['model']['num_samples'] = num_samples
    config['model']['hidden_dim'] = auto_hidden_dim
    config['training']['batch_size'] = auto_batch_size
    
    return config

def test_integrated_config():
    """测试集成配置管理"""
    print("测试集成配置管理...")
    
    # 1. 数据加载
    F_Load, A_Res = load_2_1_data(TEST_CONFIG['data']['excel_path'])
    
    # 2. 数据预处理
    start_idx = TEST_CONFIG['data']['start_idx']
    end_idx = TEST_CONFIG['data']['end_idx']
    
    # 检查数据范围
    data_length = len(F_Load)
    if end_idx > data_length:
        print(f"调整结束索引: {end_idx} -> {data_length}")
        end_idx = data_length
    
    F_D = F_Load.iloc[start_idx:end_idx, :]
    A_R = A_Res.iloc[start_idx:end_idx, :]
    
    print(f"数据范围: {start_idx}:{end_idx}")
    print(f"力信号形状: {F_D.shape}, 加速度信号形状: {A_R.shape}")
    
    # 3. 转换为numpy数组
    A_R = np.array(A_R)
    F_D = np.array(F_D)
    
    # 4. 数据预处理
    threshold = TEST_CONFIG['data']['threshold']
    train_datas_N = A_R
    Y_datas = F_D
    
    print(f"数据形状: {train_datas_N.shape}")
    print(f"使用阈值: {threshold}")
    
    # 转置数据
    train_datas_N = train_datas_N.T
    Y_datas = Y_datas.T
    
    train_datas = replace_abs_before(train_datas_N, threshold)
    
    # 5. 随机打乱
    seed = TEST_CONFIG['data']['random_seed']
    np.random.seed(seed)
    np.random.shuffle(train_datas)
    np.random.seed(seed)
    np.random.shuffle(Y_datas)
    
    # 6. 数据划分
    total_samples = train_datas.shape[0]
    train_ratio = TEST_CONFIG['data']['train_ratio']
    train_size = int(total_samples * train_ratio)
    
    print(f"总样本数: {total_samples}")
    print(f"训练集大小: {train_size}")
    print(f"测试集大小: {total_samples - train_size}")
    
    X_train = train_datas[:train_size, :]
    X_test = train_datas[train_size:, :]
    Y_train = Y_datas[:train_size, :]
    Y_test = Y_datas[train_size:, :]
    
    # 7. 自动配置参数
    config = auto_configure_params(A_R.shape, TEST_CONFIG)
    
    # 提取配置参数
    batch_size = config['training']['batch_size']
    num_epochs = config['training']['num_epochs']
    input_dim = config['model']['input_dim']
    hidden_dim = config['model']['hidden_dim']
    output_dim = config['model']['output_dim']
    seq_length = config['model']['seq_length']
    learning_rate = config['training']['learning_rate']
    window_configs = config['model']['window_configs']
    
    # 打印配置信息
    print("=" * 50)
    print("集成配置管理测试结果:")
    print(f"数据形状: {A_R.shape}")
    print(f"序列长度: {seq_length}")
    print(f"样本数量: {config['model']['num_samples']}")
    print(f"批次大小: {batch_size}")
    print(f"隐藏层维度: {hidden_dim}")
    print(f"训练轮数: {num_epochs}")
    print(f"学习率: {learning_rate}")
    print(f"注意力窗口配置: {window_configs}")
    print(f"输出目录: {config['output']['result_dir']}")
    print(f"模型文件: {config['output']['model_file']}")
    print("=" * 50)
    
    # 8. 验证配置的一致性
    print("\n配置一致性检查:")
    
    # 检查数据配置
    assert config['data']['threshold'] == 0.04, "阈值配置错误"
    assert config['data']['train_ratio'] == 0.8, "训练比例配置错误"
    
    # 检查模型配置
    assert config['model']['input_dim'] == 1, "输入维度配置错误"
    assert config['model']['output_dim'] == 1, "输出维度配置错误"
    
    # 检查训练配置
    assert config['training']['learning_rate'] == 0.001, "学习率配置错误"
    assert config['training']['loss_alpha'] == 1.0, "损失函数alpha配置错误"
    
    print("✅ 所有配置检查通过!")
    
    # 9. 测试文件路径
    import os
    result_dir = config['output']['result_dir']
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
        print(f"✅ 创建结果目录: {result_dir}")
    
    # 测试保存配置
    config_file = f"{result_dir}/test_config.txt"
    with open(config_file, 'w') as f:
        f.write("测试配置文件\n")
        f.write(f"序列长度: {seq_length}\n")
        f.write(f"批次大小: {batch_size}\n")
        f.write(f"隐藏层维度: {hidden_dim}\n")
    
    print(f"✅ 配置文件已保存: {config_file}")
    
    print("\n✅ 集成配置管理测试完成!")
    return True

if __name__ == "__main__":
    try:
        test_integrated_config()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
