# 参考文献与资源

## 核心理论基础

### 结构动力学与模态分析
1. **<PERSON>, R. W., & <PERSON>, J.** (2003). *Dynamics of structures*. McGraw-Hill.
   - 结构动力学经典教材，模态分析理论基础

2. **<PERSON><PERSON><PERSON>, D. J.** (2000). *Modal testing: theory, practice and application*. Research Studies Press.
   - 模态测试与分析的权威参考

3. **Maia, N. M. M., & Silva, J. M. M.** (1997). *Theoretical and experimental modal analysis*. Research Studies Press.
   - 理论与实验模态分析结合

### 机器学习与深度学习
4. **Goodfellow, I., Bengio, Y., & Courville, A.** (2016). *Deep learning*. MIT Press.
   - 深度学习基础理论

5. **<PERSON>, <PERSON>, et al.** (2017). Attention is all you need. *Advances in neural information processing systems*.
   - Transformer架构原始论文

6. **<PERSON>, J<PERSON>, et al.** (2018). BERT: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint*.
   - 预训练模型的经典案例

## 相关研究论文

### 结构健康监测中的机器学习
7. **<PERSON>en, K., & Manson, G.** (2007). The application of machine learning to structural health monitoring. *Philosophical Transactions of the Royal Society A*, 365(1851), 515-537.

8. **Farrar, C. R., & Worden, K.** (2012). Structural health monitoring: a machine learning perspective. John Wiley & Sons.

9. **Avci, O., et al.** (2021). A review of vibration-based damage detection in civil structures: From traditional methods to machine learning and deep learning applications. *Mechanical Systems and Signal Processing*, 147, 107077.

### 迁移学习在工程中的应用
10. **Zhang, Y., et al.** (2020). Transfer learning for structural health monitoring in bridges. *Engineering Structures*, 221, 111018.

11. **Lei, Y., et al.** (2020). Applications of machine learning to machine fault diagnosis: A review and roadmap. *Mechanical Systems and Signal Processing*, 138, 106587.

12. **Zhao, R., et al.** (2019). Deep learning and its applications to machine health monitoring. *Mechanical Systems and Signal Processing*, 115, 213-237.

### 物理信息神经网络
13. **Raissi, M., Perdikaris, P., & Karniadakis, G. E.** (2019). Physics-informed neural networks: A deep learning framework for solving forward and inverse problems involving nonlinear partial differential equations. *Journal of Computational Physics*, 378, 686-707.

14. **Karniadakis, G. E., et al.** (2021). Physics-informed machine learning. *Nature Reviews Physics*, 3(6), 422-440.

15. **Cuomo, S., et al.** (2022). Scientific machine learning through physics–informed neural networks: Where we are and what's next. *Journal of Scientific Computing*, 92(3), 1-62.

### 合成数据生成
16. **Nikolenko, S. I.** (2021). Synthetic data for deep learning. Springer.

17. **Jordon, J., et al.** (2022). Synthetic data–what, why and how? *arXiv preprint arXiv:2205.03257*.

18. **Chen, R. J., et al.** (2021). Synthetic data in machine learning for medicine and healthcare. *Nature Biomedical Engineering*, 5(6), 493-497.

## 开源工具与框架

### 结构分析软件
- **OpenSees**: 开源有限元分析平台
  - 网址: https://opensees.berkeley.edu/
  - 用途: 结构动力学分析，模态计算

- **FEniCS**: 有限元计算平台
  - 网址: https://fenicsproject.org/
  - 用途: 偏微分方程求解，结构分析

- **PyNite**: Python结构分析库
  - 网址: https://github.com/JWock82/PyNite
  - 用途: 结构分析，模态计算

### 机器学习框架
- **PyTorch**: 深度学习框架
  - 网址: https://pytorch.org/
  - 用途: 神经网络构建与训练

- **TensorFlow**: 机器学习平台
  - 网址: https://www.tensorflow.org/
  - 用途: 大规模机器学习

- **Hugging Face Transformers**: 预训练模型库
  - 网址: https://huggingface.co/transformers/
  - 用途: 预训练模型，迁移学习

### 信号处理工具
- **SciPy**: 科学计算库
  - 网址: https://scipy.org/
  - 用途: 信号处理，数值计算

- **PyWavelets**: 小波变换库
  - 网址: https://pywavelets.readthedocs.io/
  - 用途: 时频分析

- **librosa**: 音频信号处理
  - 网址: https://librosa.org/
  - 用途: 频谱分析，特征提取

## 数据集资源

### 结构健康监测数据集
- **Los Alamos National Laboratory SHM Data**
  - 描述: 标准SHM基准数据
  - 获取: 公开可用

- **IASC-ASCE SHM Task Group Benchmark**
  - 描述: 结构健康监测基准问题
  - 用途: 算法验证与比较

- **Z24 Bridge Dataset**
  - 描述: 瑞士Z24桥梁监测数据
  - 特点: 长期监测，环境变化

### 振动数据集
- **Case Western Reserve University Bearing Dataset**
  - 描述: 轴承故障诊断数据
  - 用途: 机械故障检测

- **Paderborn University Bearing Dataset**
  - 描述: 轴承全生命周期数据
  - 特点: 多种工况，完整退化过程

## 在线课程与教程

### 结构动力学
- **MIT OpenCourseWare - Structural Dynamics**
  - 网址: https://ocw.mit.edu/
  - 内容: 结构动力学基础理论

- **Coursera - Structural Dynamics**
  - 提供者: 多所大学
  - 内容: 在线结构动力学课程

### 机器学习
- **Deep Learning Specialization (Coursera)**
  - 提供者: Andrew Ng
  - 内容: 深度学习完整课程

- **CS231n: Convolutional Neural Networks (Stanford)**
  - 网址: http://cs231n.stanford.edu/
  - 内容: 卷积神经网络

- **CS224n: Natural Language Processing (Stanford)**
  - 网址: http://web.stanford.edu/class/cs224n/
  - 内容: 自然语言处理，Transformer架构

## 会议与期刊

### 主要会议
- **IMAC (International Modal Analysis Conference)**
  - 主题: 模态分析与结构动力学
  - 频率: 年度会议

- **EWSHM (European Workshop on Structural Health Monitoring)**
  - 主题: 结构健康监测
  - 频率: 双年会议

- **NeurIPS (Neural Information Processing Systems)**
  - 主题: 机器学习与人工智能
  - 频率: 年度会议

- **ICML (International Conference on Machine Learning)**
  - 主题: 机器学习
  - 频率: 年度会议

### 主要期刊
- **Mechanical Systems and Signal Processing**
  - 影响因子: 高
  - 主题: 信号处理，故障诊断

- **Structural Health Monitoring**
  - 主题: 结构健康监测
  - 出版商: SAGE

- **Journal of Sound and Vibration**
  - 主题: 声学与振动
  - 出版商: Elsevier

- **Computer Methods in Applied Mechanics and Engineering**
  - 主题: 计算力学
  - 出版商: Elsevier

## 实用工具与代码库

### 模态分析工具
- **PyOMA**: Python操作模态分析
  - 网址: https://github.com/dagghe/PyOMA
  - 功能: 操作模态分析算法

- **OpenModal**: 开源模态分析软件
  - 网址: https://github.com/openmodal/OpenModal
  - 功能: 实验模态分析

### 机器学习工具
- **scikit-learn**: 机器学习库
  - 网址: https://scikit-learn.org/
  - 功能: 传统机器学习算法

- **PyTorch Lightning**: 高级PyTorch框架
  - 网址: https://www.pytorchlightning.ai/
  - 功能: 简化深度学习训练

### 可视化工具
- **Matplotlib**: Python绘图库
  - 网址: https://matplotlib.org/
  - 功能: 科学绘图

- **Plotly**: 交互式可视化
  - 网址: https://plotly.com/
  - 功能: 交互式图表

- **Tensorboard**: 训练可视化
  - 网址: https://www.tensorflow.org/tensorboard
  - 功能: 模型训练监控

## 社区与论坛

### 学术社区
- **ResearchGate**: 学术社交网络
- **Google Scholar**: 学术搜索引擎
- **arXiv**: 预印本论文库

### 技术社区
- **Stack Overflow**: 编程问答社区
- **GitHub**: 代码托管平台
- **Reddit**: r/MachineLearning, r/engineering

### 专业组织
- **ISMA (International Conference on Noise and Vibration Engineering)**
- **SEM (Society for Experimental Mechanics)**
- **ASCE (American Society of Civil Engineers)**

这些资源为模态参数预训练研究提供了全面的理论基础、技术工具和实践指导。
