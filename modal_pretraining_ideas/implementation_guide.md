# 模态参数预训练实施指南

## 快速开始方案

### 方案一：基于有限元模型的数据生成（推荐）

#### 步骤1：建立参数化有限元模型
```python
# 伪代码示例
import numpy as np
from scipy.linalg import eigh
import pandas as pd

class ParametricFEModel:
    def __init__(self, base_model_path):
        self.base_model = self.load_model(base_model_path)
        
    def generate_modal_data(self, param_ranges, n_samples=1000):
        """生成模态参数数据集"""
        data = []
        for i in range(n_samples):
            # 随机采样参数
            params = self.sample_parameters(param_ranges)
            
            # 更新模型参数
            self.update_model_parameters(params)
            
            # 计算模态特性
            frequencies, mode_shapes, damping = self.modal_analysis()
            
            data.append({
                'parameters': params,
                'frequencies': frequencies,
                'mode_shapes': mode_shapes,
                'damping': damping
            })
        
        return data
```

#### 步骤2：预训练模型架构
```python
import torch
import torch.nn as nn

class ModalPretrainModel(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim):
        super().__init__()
        self.encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(
                d_model=hidden_dim,
                nhead=8,
                dim_feedforward=hidden_dim*4
            ),
            num_layers=6
        )
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        self.output_projection = nn.Linear(hidden_dim, output_dim)
        
    def forward(self, x):
        # x: [batch_size, seq_len, input_dim]
        x = self.input_projection(x)
        x = self.encoder(x)
        x = self.output_projection(x)
        return x
```

#### 步骤3：预训练任务设计
```python
class ModalMaskedLM:
    def __init__(self, model, mask_ratio=0.15):
        self.model = model
        self.mask_ratio = mask_ratio
        
    def create_masked_input(self, modal_data):
        """创建掩码输入"""
        batch_size, seq_len, feature_dim = modal_data.shape
        
        # 随机选择掩码位置
        mask_indices = torch.rand(batch_size, seq_len) < self.mask_ratio
        
        # 创建掩码输入
        masked_input = modal_data.clone()
        masked_input[mask_indices] = 0  # 或使用特殊掩码token
        
        return masked_input, mask_indices
        
    def compute_loss(self, predictions, targets, mask_indices):
        """计算掩码预测损失"""
        masked_predictions = predictions[mask_indices]
        masked_targets = targets[mask_indices]
        
        return nn.MSELoss()(masked_predictions, masked_targets)
```

### 方案二：基于模态叠加的响应生成

#### 步骤1：模态响应生成器
```python
class ModalResponseGenerator:
    def __init__(self, frequencies, mode_shapes, damping_ratios):
        self.frequencies = frequencies
        self.mode_shapes = mode_shapes
        self.damping_ratios = damping_ratios
        
    def generate_response(self, excitation, time_vector):
        """基于模态叠加生成响应"""
        response = np.zeros((len(time_vector), len(self.mode_shapes[0])))
        
        for i, (freq, mode, damp) in enumerate(zip(
            self.frequencies, self.mode_shapes, self.damping_ratios
        )):
            # 计算模态响应
            modal_response = self.modal_response_single_mode(
                freq, damp, excitation, time_vector
            )
            
            # 叠加到总响应
            response += np.outer(modal_response, mode)
            
        return response
        
    def modal_response_single_mode(self, freq, damp, excitation, time):
        """单模态响应计算"""
        omega_n = 2 * np.pi * freq
        omega_d = omega_n * np.sqrt(1 - damp**2)
        
        # 使用卷积计算响应（简化版）
        h = np.exp(-damp * omega_n * time) * np.sin(omega_d * time)
        response = np.convolve(excitation, h, mode='same')
        
        return response
```

#### 步骤2：数据增强策略
```python
class ModalDataAugmentation:
    def __init__(self):
        self.noise_levels = [0.01, 0.02, 0.05]
        self.frequency_shifts = [-0.05, 0, 0.05]  # ±5%
        
    def augment_modal_data(self, frequencies, mode_shapes):
        """模态数据增强"""
        augmented_data = []
        
        for noise_level in self.noise_levels:
            for freq_shift in self.frequency_shifts:
                # 频率偏移
                aug_freq = frequencies * (1 + freq_shift)
                
                # 添加噪声
                aug_modes = mode_shapes + np.random.normal(
                    0, noise_level, mode_shapes.shape
                )
                
                augmented_data.append({
                    'frequencies': aug_freq,
                    'mode_shapes': aug_modes
                })
                
        return augmented_data
```

## 微调策略实施

### 适配器微调实现
```python
class ModalAdapter(nn.Module):
    def __init__(self, hidden_dim, adapter_dim=64):
        super().__init__()
        self.down_project = nn.Linear(hidden_dim, adapter_dim)
        self.up_project = nn.Linear(adapter_dim, hidden_dim)
        self.activation = nn.ReLU()
        
    def forward(self, x):
        residual = x
        x = self.down_project(x)
        x = self.activation(x)
        x = self.up_project(x)
        return x + residual

class PretrainedModalModel(nn.Module):
    def __init__(self, pretrained_model):
        super().__init__()
        self.backbone = pretrained_model
        
        # 冻结预训练参数
        for param in self.backbone.parameters():
            param.requires_grad = False
            
        # 添加适配器
        self.adapters = nn.ModuleList([
            ModalAdapter(hidden_dim) 
            for _ in range(len(self.backbone.encoder.layers))
        ])
        
    def forward(self, x):
        # 在每层后添加适配器
        for layer, adapter in zip(self.backbone.encoder.layers, self.adapters):
            x = layer(x)
            x = adapter(x)
        return x
```

### 域适应微调
```python
class DomainAdaptationTrainer:
    def __init__(self, model, domain_discriminator):
        self.model = model
        self.domain_discriminator = domain_discriminator
        self.lambda_domain = 0.1
        
    def train_step(self, synthetic_data, real_data):
        # 特征提取
        synthetic_features = self.model.extract_features(synthetic_data)
        real_features = self.model.extract_features(real_data)
        
        # 域分类损失
        synthetic_domain_pred = self.domain_discriminator(synthetic_features)
        real_domain_pred = self.domain_discriminator(real_features)
        
        domain_loss = self.compute_domain_loss(
            synthetic_domain_pred, real_domain_pred
        )
        
        # 任务损失
        task_loss = self.compute_task_loss(synthetic_data, real_data)
        
        # 总损失
        total_loss = task_loss - self.lambda_domain * domain_loss
        
        return total_loss
```

## 评估与验证

### 性能评估框架
```python
class ModalModelEvaluator:
    def __init__(self):
        self.metrics = {
            'frequency_error': self.frequency_error,
            'mac_value': self.modal_assurance_criterion,
            'response_rmse': self.response_rmse
        }
        
    def evaluate(self, model, test_data):
        """全面评估模型性能"""
        results = {}
        
        for metric_name, metric_func in self.metrics.items():
            results[metric_name] = metric_func(model, test_data)
            
        return results
        
    def frequency_error(self, model, test_data):
        """频率预测误差"""
        pred_freq = model.predict_frequencies(test_data)
        true_freq = test_data['frequencies']
        
        return np.mean(np.abs(pred_freq - true_freq) / true_freq)
        
    def modal_assurance_criterion(self, model, test_data):
        """模态保证准则"""
        pred_modes = model.predict_mode_shapes(test_data)
        true_modes = test_data['mode_shapes']
        
        mac_values = []
        for pred, true in zip(pred_modes, true_modes):
            mac = np.abs(np.dot(pred, true))**2 / (
                np.dot(pred, pred) * np.dot(true, true)
            )
            mac_values.append(mac)
            
        return np.mean(mac_values)
```

## 部署与应用

### 模型部署流程
```python
class ModalModelDeployment:
    def __init__(self, model_path):
        self.model = self.load_model(model_path)
        self.preprocessor = self.load_preprocessor()
        
    def predict(self, input_data):
        """在线预测接口"""
        # 数据预处理
        processed_data = self.preprocessor.transform(input_data)
        
        # 模型预测
        with torch.no_grad():
            predictions = self.model(processed_data)
            
        # 后处理
        results = self.postprocess(predictions)
        
        return results
        
    def batch_predict(self, batch_data):
        """批量预测接口"""
        results = []
        for data in batch_data:
            result = self.predict(data)
            results.append(result)
        return results
```

## 最佳实践建议

### 1. 数据质量控制
- 确保合成数据的物理一致性
- 验证模态参数的合理性
- 控制数据的多样性和平衡性

### 2. 训练策略优化
- 使用渐进式训练策略
- 合理设置学习率调度
- 监控训练过程中的物理约束

### 3. 模型验证
- 使用多种评估指标
- 进行跨域验证测试
- 检查模型的物理合理性

### 4. 部署考虑
- 优化模型推理速度
- 确保数值稳定性
- 提供不确定性量化

这个实施指南提供了从数据生成到模型部署的完整技术路径，可以作为实际项目的参考框架。
