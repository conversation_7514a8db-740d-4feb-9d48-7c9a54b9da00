#!/usr/bin/env python3
"""
Jupyter Notebook <-> Python 文件转换工具

功能：
1. 将 .ipynb 文件转换为 .py 文件，保留单元格边界标记
2. 将带标记的 .py 文件转换回 .ipynb 文件

重要提示：
- 请勿删除 # <CELL_BOUNDARY> 标记，这些标记用于重新转换为notebook
- 请勿删除 # <CELL_TYPE:code> 或 # <CELL_TYPE:markdown> 标记
- 请勿删除 # <CELL_END> 标记

使用方法：
python notebook_converter.py notebook_to_py input.ipynb output.py
python notebook_converter.py py_to_notebook input.py output.ipynb
"""

import json
import sys
import os
import argparse
from typing import List, Dict, Any


class NotebookConverter:
    """Jupyter Notebook 和 Python 文件的双向转换器"""
    
    # 重要：这些标记用于保持单元格边界，请勿在编辑时删除！
    CELL_BOUNDARY_START = "# <CELL_BOUNDARY>"
    CELL_TYPE_PREFIX = "# <CELL_TYPE:"
    CELL_END = "# <CELL_END>"
    WARNING_COMMENT = """# ==========================================
# 重要提示：请勿删除以下类型的标记行！
# - # <CELL_BOUNDARY>
# - # <CELL_TYPE:code> 或 # <CELL_TYPE:markdown>  
# - # <CELL_END>
# 这些标记用于重新转换为Jupyter notebook
# ==========================================

"""

    def notebook_to_py(self, notebook_path: str, output_path: str) -> None:
        """将Jupyter notebook转换为Python文件"""
        try:
            with open(notebook_path, 'r', encoding='utf-8') as f:
                notebook = json.load(f)
        except FileNotFoundError:
            print(f"错误：找不到文件 {notebook_path}")
            return
        except json.JSONDecodeError:
            print(f"错误：{notebook_path} 不是有效的JSON文件")
            return

        with open(output_path, 'w', encoding='utf-8') as f:
            # 写入警告注释
            f.write(self.WARNING_COMMENT)
            
            # 写入文件头信息
            f.write(f'# 从 {os.path.basename(notebook_path)} 转换而来\n')
            f.write(f'# 使用 notebook_converter.py 工具转换\n\n')
            
            cells = notebook.get('cells', [])
            
            for i, cell in enumerate(cells):
                cell_type = cell.get('cell_type', 'code')
                source = cell.get('source', [])
                
                # 写入单元格边界标记
                f.write(f"{self.CELL_BOUNDARY_START}\n")
                f.write(f"{self.CELL_TYPE_PREFIX}{cell_type}>\n")
                
                if cell_type == 'code':
                    # 代码单元格直接写入
                    if isinstance(source, list):
                        for line in source:
                            f.write(line)
                            if not line.endswith('\n'):
                                f.write('\n')
                    else:
                        f.write(source)
                        if not source.endswith('\n'):
                            f.write('\n')
                            
                elif cell_type == 'markdown':
                    # Markdown单元格转为注释
                    f.write('"""\n')
                    if isinstance(source, list):
                        for line in source:
                            f.write(line)
                            if not line.endswith('\n'):
                                f.write('\n')
                    else:
                        f.write(source)
                        if not source.endswith('\n'):
                            f.write('\n')
                    f.write('"""\n')
                
                # 写入单元格结束标记
                f.write(f"{self.CELL_END}\n\n")
        
        print(f"成功转换：{notebook_path} -> {output_path}")
        print("重要提示：请勿删除文件中的 <CELL_BOUNDARY>、<CELL_TYPE> 和 <CELL_END> 标记！")

    def py_to_notebook(self, py_path: str, output_path: str) -> None:
        """将带标记的Python文件转换回Jupyter notebook"""
        try:
            with open(py_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except FileNotFoundError:
            print(f"错误：找不到文件 {py_path}")
            return

        # 解析单元格
        cells = self._parse_cells_from_py(content)
        
        # 创建notebook结构
        notebook = {
            "cells": cells,
            "metadata": {
                "kernelspec": {
                    "display_name": "Python 3",
                    "language": "python",
                    "name": "python3"
                },
                "language_info": {
                    "name": "python",
                    "version": "3.8.0"
                }
            },
            "nbformat": 4,
            "nbformat_minor": 4
        }
        
        # 保存notebook
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(notebook, f, indent=2, ensure_ascii=False)
        
        print(f"成功转换：{py_path} -> {output_path}")

    def _parse_cells_from_py(self, content: str) -> List[Dict[str, Any]]:
        """从Python文件内容解析单元格"""
        lines = content.split('\n')
        cells = []
        current_cell = None
        current_cell_lines = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            if line.strip() == self.CELL_BOUNDARY_START:
                # 保存前一个单元格
                if current_cell is not None:
                    current_cell['source'] = current_cell_lines
                    cells.append(current_cell)
                
                # 开始新单元格
                current_cell_lines = []
                i += 1
                
                # 读取单元格类型
                if i < len(lines) and lines[i].startswith(self.CELL_TYPE_PREFIX):
                    cell_type_line = lines[i]
                    cell_type = cell_type_line[len(self.CELL_TYPE_PREFIX):-1]  # 移除 '>'
                    current_cell = {
                        "cell_type": cell_type,
                        "metadata": {},
                        "source": []
                    }
                    if cell_type == "code":
                        current_cell["execution_count"] = None
                        current_cell["outputs"] = []
                    i += 1
                else:
                    # 默认为代码单元格
                    current_cell = {
                        "cell_type": "code",
                        "execution_count": None,
                        "metadata": {},
                        "outputs": [],
                        "source": []
                    }
            
            elif line.strip() == self.CELL_END:
                # 单元格结束，继续下一行
                i += 1
                continue
            
            elif current_cell is not None:
                # 处理单元格内容
                if current_cell["cell_type"] == "markdown":
                    # 处理markdown单元格（移除三引号）
                    if line.strip() == '"""':
                        i += 1
                        continue
                
                current_cell_lines.append(line + '\n')
            
            i += 1
        
        # 保存最后一个单元格
        if current_cell is not None:
            current_cell['source'] = current_cell_lines
            cells.append(current_cell)
        
        return cells


def main():
    parser = argparse.ArgumentParser(description='Jupyter Notebook 和 Python 文件双向转换工具')
    parser.add_argument('command', choices=['notebook_to_py', 'py_to_notebook'], 
                       help='转换命令')
    parser.add_argument('input_file', help='输入文件路径')
    parser.add_argument('output_file', help='输出文件路径')
    
    args = parser.parse_args()
    
    converter = NotebookConverter()
    
    if args.command == 'notebook_to_py':
        converter.notebook_to_py(args.input_file, args.output_file)
    elif args.command == 'py_to_notebook':
        converter.py_to_notebook(args.input_file, args.output_file)


if __name__ == "__main__":
    main()
