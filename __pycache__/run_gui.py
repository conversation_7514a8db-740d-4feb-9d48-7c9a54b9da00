#!/usr/bin/env python3
"""
启动 Jupyter Notebook 转换工具 GUI

这个脚本用于启动图形界面版本的转换工具
"""

import sys
import os

def check_requirements():
    """检查运行环境"""
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        print("❌ tkinter 不可用，请安装 tkinter")
        print("在 Ubuntu/Debian 上: sudo apt-get install python3-tk")
        print("在 macOS 上: tkinter 通常已包含在 Python 中")
        print("在 Windows 上: tkinter 通常已包含在 Python 中")
        return False
    
    try:
        import json
        print("✅ json 模块可用")
    except ImportError:
        print("❌ json 模块不可用")
        return False
    
    return True

def main():
    print("=== Jupyter Notebook 转换工具 GUI 启动器 ===\n")
    
    # 检查环境
    if not check_requirements():
        print("\n❌ 环境检查失败，无法启动 GUI")
        return
    
    # 检查 GUI 文件是否存在
    gui_file = "notebook_converter_gui.py"
    if not os.path.exists(gui_file):
        print(f"❌ 找不到 GUI 文件: {gui_file}")
        print("请确保在正确的目录中运行此脚本")
        return
    
    print("✅ 环境检查通过")
    print(f"✅ 找到 GUI 文件: {gui_file}")
    print("\n🚀 启动 GUI 界面...")
    
    try:
        # 导入并启动 GUI
        from notebook_converter_gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"❌ 启动 GUI 失败: {e}")
        print("\n可能的解决方案:")
        print("1. 确保您在图形界面环境中运行（不是纯命令行环境）")
        print("2. 检查 DISPLAY 环境变量是否设置正确")
        print("3. 如果在远程服务器上，请使用 X11 转发或 VNC")
        print("4. 尝试使用命令行版本: python3 notebook_converter.py")

if __name__ == "__main__":
    main()
