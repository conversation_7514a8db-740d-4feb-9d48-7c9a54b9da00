# Jupyter Notebook 转换工具

这个工具可以在 Jupyter Notebook (.ipynb) 和 Python (.py) 文件之间进行双向转换，并保留单元格边界信息。

## 功能特点

1. **双向转换**：支持 notebook → Python 和 Python → notebook
2. **保留结构**：使用特殊标记保留单元格边界和类型信息
3. **安全标记**：包含警告注释，提醒用户不要删除重要标记
4. **图形界面**：提供易用的 GUI 界面
5. **命令行支持**：同时支持命令行操作

## 使用方法

### GUI 版本（推荐）

1. **启动 GUI 界面**：
```bash
python3 run_gui.py
```
或直接运行：
```bash
python3 notebook_converter_gui.py
```

2. **GUI 功能**：
   - 选择转换模式（Notebook → Python 或 Python → Notebook）
   - 通过浏览按钮选择输入和输出文件
   - 自动生成输出文件名
   - 实时显示转换日志
   - 进度指示器
   - 重要提示和警告

### 命令行版本

1. **Notebook 转 Python**：
```bash
python3 notebook_converter.py notebook_to_py input.ipynb output.py
```

2. **Python 转 Notebook**：
```bash
python3 notebook_converter.py py_to_notebook input.py output.ipynb
```

## 重要提示 ⚠️

转换后的 Python 文件包含特殊标记，**请勿删除**以下类型的行：

- `# <CELL_BOUNDARY>`
- `# <CELL_TYPE:code>` 或 `# <CELL_TYPE:markdown>`
- `# <CELL_END>`

这些标记用于重新转换为 Jupyter notebook。

## 转换示例

### 原始 Notebook 单元格：
```python
# 代码单元格 1
import pandas as pd
print("Hello World")
```

```markdown
# Markdown 单元格
这是一个说明文档
```

### 转换后的 Python 文件：
```python
# ==========================================
# 重要提示：请勿删除以下类型的标记行！
# - # <CELL_BOUNDARY>
# - # <CELL_TYPE:code> 或 # <CELL_TYPE:markdown>  
# - # <CELL_END>
# 这些标记用于重新转换为Jupyter notebook
# ==========================================

# 从 example.ipynb 转换而来
# 使用 notebook_converter.py 工具转换

# <CELL_BOUNDARY>
# <CELL_TYPE:code>
import pandas as pd
print("Hello World")
# <CELL_END>

# <CELL_BOUNDARY>
# <CELL_TYPE:markdown>
"""
# Markdown 单元格
这是一个说明文档
"""
# <CELL_END>
```

## 工作流程建议

1. 将 notebook 转换为 Python 文件进行编辑
2. 在 Python 文件中进行代码修改（保留标记）
3. 完成修改后转换回 notebook 格式
4. 在 Jupyter 中运行和测试

## 文件结构

项目包含以下文件：

- `notebook_converter_gui.py` - GUI 版本转换工具（主要文件）
- `notebook_converter.py` - 命令行版本转换工具
- `run_gui.py` - GUI 启动器和环境检查
- `example_usage.py` - 命令行版本使用示例
- `test_example.ipynb` - 测试用的示例 notebook
- `README_notebook_converter.md` - 本说明文档

## 环境要求

- Python 3.6+
- tkinter（GUI 版本需要，通常已包含在 Python 中）
- json（标准库）

### 安装 tkinter（如果需要）

- **Ubuntu/Debian**: `sudo apt-get install python3-tk`
- **macOS**: 通常已包含在 Python 中
- **Windows**: 通常已包含在 Python 中

## 快速开始

1. **使用 GUI 版本**（推荐新手）：
```bash
python3 run_gui.py
```

2. **使用命令行版本**（适合脚本自动化）：
```bash
# 转换 notebook 为 Python
python3 notebook_converter.py notebook_to_py example.ipynb example.py

# 转换 Python 为 notebook
python3 notebook_converter.py py_to_notebook example.py example_new.ipynb
```

3. **运行测试示例**：
```bash
python3 example_usage.py
```

## 注意事项

- 转换过程会保留代码单元格的原始内容
- Markdown 单元格会被转换为三引号包围的文档字符串
- 输出结果和执行计数不会被保留（转换回 notebook 时会重置）
- 请确保输入文件路径正确且文件存在
- GUI 版本需要图形界面环境

## 错误处理

工具会处理以下常见错误：
- 文件不存在
- JSON 格式错误
- 编码问题
- tkinter 不可用（GUI 版本）

如遇到问题，请检查文件路径和格式是否正确。
