#!/usr/bin/env python3
"""
Jupyter Notebook 转换工具 - GUI版本

功能：
1. 将 .ipynb 文件转换为 .py 文件，保留单元格边界标记
2. 将带标记的 .py 文件转换回 .ipynb 文件

使用图形界面进行操作，支持文件选择和拖拽
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import threading
from typing import List, Dict, Any


class NotebookConverterGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Jupyter Notebook 转换工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        self.setup_ui()
        self.converter = NotebookConverter()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Jupyter Notebook 转换工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 转换模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="转换模式", padding="10")
        mode_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        mode_frame.columnconfigure(1, weight=1)
        
        self.mode_var = tk.StringVar(value="notebook_to_py")
        
        ttk.Radiobutton(mode_frame, text="Notebook → Python (.ipynb → .py)", 
                       variable=self.mode_var, value="notebook_to_py",
                       command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        
        ttk.Radiobutton(mode_frame, text="Python → Notebook (.py → .ipynb)", 
                       variable=self.mode_var, value="py_to_notebook",
                       command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W)
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 输入文件
        ttk.Label(file_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        self.input_file_var = tk.StringVar()
        input_entry = ttk.Entry(file_frame, textvariable=self.input_file_var, width=50)
        input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        
        ttk.Button(file_frame, text="浏览...", 
                  command=self.browse_input_file).grid(row=0, column=2, pady=(0, 5))
        
        # 输出文件
        ttk.Label(file_frame, text="输出文件:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        self.output_file_var = tk.StringVar()
        output_entry = ttk.Entry(file_frame, textvariable=self.output_file_var, width=50)
        output_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(5, 0))
        
        ttk.Button(file_frame, text="浏览...", 
                  command=self.browse_output_file).grid(row=1, column=2, pady=(5, 0))
        
        # 转换按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=10)
        
        self.convert_button = ttk.Button(button_frame, text="开始转换", 
                                        command=self.start_conversion,
                                        style='Accent.TButton')
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="清空", 
                  command=self.clear_all).pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 重要提示
        warning_frame = ttk.LabelFrame(main_frame, text="重要提示", padding="10")
        warning_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        warning_text = ("⚠️ 转换后的Python文件包含特殊标记，请勿删除以下类型的行：\n"
                       "• # <CELL_BOUNDARY>\n"
                       "• # <CELL_TYPE:code> 或 # <CELL_TYPE:markdown>\n"
                       "• # <CELL_END>\n"
                       "这些标记用于重新转换为Jupyter notebook。")
        
        warning_label = ttk.Label(warning_frame, text=warning_text, 
                                 foreground='red', font=('Arial', 9))
        warning_label.grid(row=0, column=0, sticky=tk.W)
        
        # 初始化界面状态
        self.on_mode_change()
        
    def on_mode_change(self):
        """模式改变时更新界面"""
        mode = self.mode_var.get()
        if mode == "notebook_to_py":
            self.log("模式切换: Notebook → Python")
        else:
            self.log("模式切换: Python → Notebook")
            
    def browse_input_file(self):
        """浏览输入文件"""
        mode = self.mode_var.get()
        if mode == "notebook_to_py":
            filetypes = [("Jupyter Notebook", "*.ipynb"), ("所有文件", "*.*")]
        else:
            filetypes = [("Python文件", "*.py"), ("所有文件", "*.*")]
            
        filename = filedialog.askopenfilename(
            title="选择输入文件",
            filetypes=filetypes
        )
        
        if filename:
            self.input_file_var.set(filename)
            # 自动生成输出文件名
            self.auto_generate_output_filename(filename)
            
    def browse_output_file(self):
        """浏览输出文件"""
        mode = self.mode_var.get()
        if mode == "notebook_to_py":
            filetypes = [("Python文件", "*.py"), ("所有文件", "*.*")]
            default_ext = ".py"
        else:
            filetypes = [("Jupyter Notebook", "*.ipynb"), ("所有文件", "*.*")]
            default_ext = ".ipynb"
            
        filename = filedialog.asksaveasfilename(
            title="选择输出文件",
            filetypes=filetypes,
            defaultextension=default_ext
        )
        
        if filename:
            self.output_file_var.set(filename)
            
    def auto_generate_output_filename(self, input_filename):
        """自动生成输出文件名"""
        mode = self.mode_var.get()
        base_name = os.path.splitext(input_filename)[0]
        
        if mode == "notebook_to_py":
            output_filename = base_name + ".py"
        else:
            output_filename = base_name + "_converted.ipynb"
            
        self.output_file_var.set(output_filename)
        
    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_all(self):
        """清空所有输入"""
        self.input_file_var.set("")
        self.output_file_var.set("")
        self.log_text.delete(1.0, tk.END)
        self.log("界面已清空")
        
    def start_conversion(self):
        """开始转换（在新线程中执行）"""
        input_file = self.input_file_var.get().strip()
        output_file = self.output_file_var.get().strip()
        
        if not input_file:
            messagebox.showerror("错误", "请选择输入文件")
            return
            
        if not output_file:
            messagebox.showerror("错误", "请指定输出文件")
            return
            
        if not os.path.exists(input_file):
            messagebox.showerror("错误", f"输入文件不存在: {input_file}")
            return
            
        # 在新线程中执行转换
        self.convert_button.config(state='disabled')
        self.progress.start()
        
        thread = threading.Thread(target=self.perform_conversion, 
                                 args=(input_file, output_file))
        thread.daemon = True
        thread.start()
        
    def perform_conversion(self, input_file, output_file):
        """执行转换操作"""
        try:
            mode = self.mode_var.get()
            self.log(f"开始转换: {os.path.basename(input_file)} → {os.path.basename(output_file)}")
            
            if mode == "notebook_to_py":
                self.converter.notebook_to_py(input_file, output_file, self.log)
            else:
                self.converter.py_to_notebook(input_file, output_file, self.log)
                
            self.log("✅ 转换完成!")
            
            # 在主线程中显示成功消息
            self.root.after(0, lambda: messagebox.showinfo("成功", 
                f"转换完成!\n输出文件: {output_file}"))
                
        except Exception as e:
            error_msg = f"转换失败: {str(e)}"
            self.log(f"❌ {error_msg}")
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
            
        finally:
            # 在主线程中重置界面状态
            self.root.after(0, self.conversion_finished)
            
    def conversion_finished(self):
        """转换完成后的界面重置"""
        self.progress.stop()
        self.convert_button.config(state='normal')


class NotebookConverter:
    """Jupyter Notebook 和 Python 文件的双向转换器"""
    
    CELL_BOUNDARY_START = "# <CELL_BOUNDARY>"
    CELL_TYPE_PREFIX = "# <CELL_TYPE:"
    CELL_END = "# <CELL_END>"
    WARNING_COMMENT = """# ==========================================
# 重要提示：请勿删除以下类型的标记行！
# - # <CELL_BOUNDARY>
# - # <CELL_TYPE:code> 或 # <CELL_TYPE:markdown>  
# - # <CELL_END>
# 这些标记用于重新转换为Jupyter notebook
# ==========================================

"""

    def notebook_to_py(self, notebook_path: str, output_path: str, log_func=None) -> None:
        """将Jupyter notebook转换为Python文件"""
        if log_func is None:
            log_func = print
            
        try:
            with open(notebook_path, 'r', encoding='utf-8') as f:
                notebook = json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到文件 {notebook_path}")
        except json.JSONDecodeError:
            raise ValueError(f"{notebook_path} 不是有效的JSON文件")

        with open(output_path, 'w', encoding='utf-8') as f:
            # 写入警告注释
            f.write(self.WARNING_COMMENT)
            
            # 写入文件头信息
            f.write(f'# 从 {os.path.basename(notebook_path)} 转换而来\n')
            f.write(f'# 使用 notebook_converter_gui.py 工具转换\n\n')
            
            cells = notebook.get('cells', [])
            log_func(f"处理 {len(cells)} 个单元格...")
            
            for i, cell in enumerate(cells):
                cell_type = cell.get('cell_type', 'code')
                source = cell.get('source', [])
                
                log_func(f"处理单元格 {i+1}/{len(cells)} (类型: {cell_type})")
                
                # 写入单元格边界标记
                f.write(f"{self.CELL_BOUNDARY_START}\n")
                f.write(f"{self.CELL_TYPE_PREFIX}{cell_type}>\n")
                
                if cell_type == 'code':
                    # 代码单元格直接写入
                    if isinstance(source, list):
                        for line in source:
                            f.write(line)
                            if not line.endswith('\n'):
                                f.write('\n')
                    else:
                        f.write(source)
                        if not source.endswith('\n'):
                            f.write('\n')
                            
                elif cell_type == 'markdown':
                    # Markdown单元格转为注释
                    f.write('"""\n')
                    if isinstance(source, list):
                        for line in source:
                            f.write(line)
                            if not line.endswith('\n'):
                                f.write('\n')
                    else:
                        f.write(source)
                        if not source.endswith('\n'):
                            f.write('\n')
                    f.write('"""\n')
                
                # 写入单元格结束标记
                f.write(f"{self.CELL_END}\n\n")
        
        log_func(f"成功转换: {notebook_path} → {output_path}")

    def py_to_notebook(self, py_path: str, output_path: str, log_func=None) -> None:
        """将带标记的Python文件转换回Jupyter notebook"""
        if log_func is None:
            log_func = print
            
        try:
            with open(py_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到文件 {py_path}")

        log_func("解析Python文件中的单元格...")
        # 解析单元格
        cells = self._parse_cells_from_py(content, log_func)
        
        log_func(f"找到 {len(cells)} 个单元格")
        
        # 创建notebook结构
        notebook = {
            "cells": cells,
            "metadata": {
                "kernelspec": {
                    "display_name": "Python 3",
                    "language": "python",
                    "name": "python3"
                },
                "language_info": {
                    "name": "python",
                    "version": "3.8.0"
                }
            },
            "nbformat": 4,
            "nbformat_minor": 4
        }
        
        # 保存notebook
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(notebook, f, indent=2, ensure_ascii=False)
        
        log_func(f"成功转换: {py_path} → {output_path}")

    def _parse_cells_from_py(self, content: str, log_func=None) -> List[Dict[str, Any]]:
        """从Python文件内容解析单元格"""
        if log_func is None:
            log_func = print
            
        lines = content.split('\n')
        cells = []
        current_cell = None
        current_cell_lines = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            if line.strip() == self.CELL_BOUNDARY_START:
                # 保存前一个单元格
                if current_cell is not None:
                    current_cell['source'] = current_cell_lines
                    cells.append(current_cell)
                
                # 开始新单元格
                current_cell_lines = []
                i += 1
                
                # 读取单元格类型
                if i < len(lines) and lines[i].startswith(self.CELL_TYPE_PREFIX):
                    cell_type_line = lines[i]
                    cell_type = cell_type_line[len(self.CELL_TYPE_PREFIX):-1]  # 移除 '>'
                    current_cell = {
                        "cell_type": cell_type,
                        "metadata": {},
                        "source": []
                    }
                    if cell_type == "code":
                        current_cell["execution_count"] = None
                        current_cell["outputs"] = []
                    i += 1
                else:
                    # 默认为代码单元格
                    current_cell = {
                        "cell_type": "code",
                        "execution_count": None,
                        "metadata": {},
                        "outputs": [],
                        "source": []
                    }
            
            elif line.strip() == self.CELL_END:
                # 单元格结束，继续下一行
                i += 1
                continue
            
            elif current_cell is not None:
                # 处理单元格内容
                if current_cell["cell_type"] == "markdown":
                    # 处理markdown单元格（移除三引号）
                    if line.strip() == '"""':
                        i += 1
                        continue
                
                current_cell_lines.append(line + '\n')
            
            i += 1
        
        # 保存最后一个单元格
        if current_cell is not None:
            current_cell['source'] = current_cell_lines
            cells.append(current_cell)
        
        return cells


def main():
    root = tk.Tk()
    app = NotebookConverterGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
