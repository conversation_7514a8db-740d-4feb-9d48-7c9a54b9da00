#!/usr/bin/env python3
"""
模型配置文件
用户可以在这里调整各种参数设置
"""

# ==================== 数据配置 ====================
DATA_CONFIG = {
    # Excel文件路径
    'excel_path': '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx',
    
    # 数据范围设置
    'start_idx': 0,      # 起始行索引
    'end_idx': 400,      # 结束行索引
    
    # 数据预处理
    'threshold': 0.04,   # 数据预处理阈值
    'train_ratio': 0.9,  # 训练集比例
    'random_seed': 400,  # 随机种子
}

# ==================== 模型配置 ====================
MODEL_CONFIG = {
    # 基础参数（会被自动配置覆盖，除非在USER_OVERRIDE中指定）
    'input_dim': 1,
    'output_dim': 1,
    'hidden_dim': 64,     # 隐藏层维度，会根据序列长度自动调整
    
    # Transformer特定参数
    'num_layers': 2,
    'window_configs': [(1,5), (2,3)],  # 注意力窗口配置 [(before1,after1), (before2,after2)]
    
    # 位置编码
    'max_pos_encoding': 1000,  # 位置编码最大长度
}

# ==================== 训练配置 ====================
TRAINING_CONFIG = {
    # 训练参数（会被自动配置覆盖，除非在USER_OVERRIDE中指定）
    'batch_size': 8,
    'num_epochs': 10,
    'learning_rate': 0.0005,
    
    # 损失函数参数
    'loss_alpha': 1.0,    # MSE损失权重
    'loss_beta': 1.0,     # 梯度损失权重
    
    # 优化器参数
    'optimizer': 'Adam',
    'weight_decay': 0.0,
    
    # 学习率调度
    'use_scheduler': False,
    'scheduler_step_size': 30,
    'scheduler_gamma': 0.1,
}

# ==================== 用户覆盖配置 ====================
# 在这里指定的参数会覆盖自动配置的参数
USER_OVERRIDE = {
    # 例如：强制使用特定的批次大小和学习率
    # 'batch_size': 16,
    # 'learning_rate': 0.001,
    # 'hidden_dim': 128,
    # 'num_epochs': 50,
    
    # 如果不想覆盖任何自动配置，保持为空字典
}

# ==================== 输出配置 ====================
OUTPUT_CONFIG = {
    # 结果保存路径
    'result_dir': './result',
    'model_dir': './models',
    
    # 文件名
    'scaler_file': 'scaler_X.csv',
    'model_file': 'transformer_model_auto.pth',
    'peak_errors_file': 'X_peak_errors.csv',
    'r2_scores_file': 'X_R_2.csv',
    'relative_errors_file': 'X-relative_errors.csv',
    'analysis_data_file': 'analysis_data.csv',
    
    # 可视化设置
    'save_plots': True,
    'plot_dpi': 300,
    'plot_format': 'png',
}

# ==================== 自动配置函数 ====================
def get_auto_config(A_R_shape, F_D_shape):
    """
    根据数据形状自动生成配置
    
    参数:
    A_R_shape: 加速度数据形状 (时间点数, 样本数)
    F_D_shape: 力信号数据形状 (时间点数, 样本数)
    
    返回:
    完整的配置字典
    """
    # 基础配置
    config = {
        'data': DATA_CONFIG.copy(),
        'model': MODEL_CONFIG.copy(),
        'training': TRAINING_CONFIG.copy(),
        'output': OUTPUT_CONFIG.copy()
    }
    
    # 自动计算参数
    seq_length = A_R_shape[0]
    num_samples = A_R_shape[1]
    
    # 根据序列长度自动调整隐藏层维度
    if seq_length < 200:
        auto_hidden_dim = 32
    elif seq_length < 500:
        auto_hidden_dim = 64
    else:
        auto_hidden_dim = 128
    
    # 根据样本数量自动调整批次大小
    auto_batch_size = min(TRAINING_CONFIG['batch_size'], max(1, num_samples // 10))
    
    # 更新自动配置
    config['model']['seq_length'] = seq_length
    config['model']['num_samples'] = num_samples
    
    # 只有在用户没有覆盖的情况下才使用自动配置
    if 'hidden_dim' not in USER_OVERRIDE:
        config['model']['hidden_dim'] = auto_hidden_dim
    
    if 'batch_size' not in USER_OVERRIDE:
        config['training']['batch_size'] = auto_batch_size
    
    # 应用用户覆盖配置
    for key, value in USER_OVERRIDE.items():
        if key in config['model']:
            config['model'][key] = value
        elif key in config['training']:
            config['training'][key] = value
        elif key in config['data']:
            config['data'][key] = value
        elif key in config['output']:
            config['output'][key] = value
    
    return config

def print_config(config):
    """打印配置信息"""
    print("=" * 60)
    print("模型配置信息:")
    print("=" * 60)
    
    print("\n数据配置:")
    for key, value in config['data'].items():
        print(f"  {key}: {value}")
    
    print("\n模型配置:")
    for key, value in config['model'].items():
        print(f"  {key}: {value}")
    
    print("\n训练配置:")
    for key, value in config['training'].items():
        print(f"  {key}: {value}")
    
    print("\n输出配置:")
    for key, value in config['output'].items():
        print(f"  {key}: {value}")
    
    print("=" * 60)

# ==================== 使用示例 ====================
if __name__ == "__main__":
    # 模拟数据形状
    A_R_shape = (500, 80)  # 500个时间点，80个样本
    F_D_shape = (500, 80)
    
    # 获取自动配置
    config = get_auto_config(A_R_shape, F_D_shape)
    
    # 打印配置
    print_config(config)
