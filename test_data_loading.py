#!/usr/bin/env python3
"""
测试Excel数据加载功能
验证修改后的代码是否能正确加载和处理数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def load_excel_data(excel_path):
    """
    从Excel文件加载2-1A1相关的力信号和加速度信号数据
    
    参数:
    excel_path: Excel文件路径
    
    返回:
    F_data: 力信号数据 (DataFrame)
    A_data: 加速度信号数据 (DataFrame)
    """
    print("正在加载Excel数据...")
    
    # 读取力信号数据
    force_df = pd.read_excel(excel_path, sheet_name='Force_Data')
    print(f"力信号工作表形状: {force_df.shape}")
    
    # 读取加速度信号数据
    accel_df = pd.read_excel(excel_path, sheet_name='Acceleration_Data')
    print(f"加速度信号工作表形状: {accel_df.shape}")
    
    # 获取所有2-1F列（力信号）
    force_columns = [col for col in force_df.columns if col.startswith('2-1F-')]
    print(f"找到 {len(force_columns)} 个2-1F力信号列")
    
    # 获取所有2-1A1列（A1传感器的加速度信号）
    accel_columns = [col for col in accel_df.columns if col.startswith('2-1A1-')]
    print(f"找到 {len(accel_columns)} 个2-1A1加速度信号列")
    
    # 提取匹配的F和A1信号对
    matched_pairs = []
    for f_col in force_columns:
        # 从力信号列名提取事件编号，如 '2-1F-1' -> '1'
        event_num = f_col.split('-')[-1]
        # 构造对应的A1列名
        a_col = f'2-1A1-{event_num}'
        
        if a_col in accel_columns:
            matched_pairs.append((f_col, a_col))
    
    print(f"找到 {len(matched_pairs)} 对匹配的F-A1信号")
    
    # 提取匹配的数据
    if matched_pairs:
        # 提取力信号数据
        force_data = force_df[[pair[0] for pair in matched_pairs]]
        # 提取加速度信号数据
        accel_data = accel_df[[pair[1] for pair in matched_pairs]]
        
        print(f"提取的力信号数据形状: {force_data.shape}")
        print(f"提取的加速度信号数据形状: {accel_data.shape}")
        
        return force_data, accel_data, matched_pairs
    else:
        raise ValueError("未找到匹配的F-A1信号对")

def test_data_loading():
    """测试数据加载功能"""
    excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
    
    try:
        # 加载数据
        F_Load, A_Res, matched_pairs = load_excel_data(excel_path)
        
        # 显示匹配的信号对信息
        print("\n匹配的信号对:")
        for i, (f_col, a_col) in enumerate(matched_pairs[:10]):  # 显示前10对
            print(f"  {i+1}. {f_col} <-> {a_col}")
        if len(matched_pairs) > 10:
            print(f"  ... 还有 {len(matched_pairs) - 10} 对")
        
        # 数据预处理：直接指定需要的数据范围
        start_idx = 900  # 起始行索引
        end_idx = 1100   # 结束行索引

        # 检查数据范围是否有效
        data_length = len(F_Load)
        if end_idx > data_length:
            print(f"警告: 指定的结束索引 {end_idx} 超过数据长度 {data_length}，将调整为 {data_length}")
            end_idx = data_length
        if start_idx < 0:
            start_idx = 0
        if start_idx >= end_idx:
            raise ValueError(f"起始索引 {start_idx} 必须小于结束索引 {end_idx}")
        
        F_D = F_Load.iloc[start_idx:end_idx, :]
        A_R = A_Res.iloc[start_idx:end_idx, :]
        
        print(f"\n选择的数据范围: {start_idx}:{end_idx}")
        print(f"处理后的力信号形状: {F_D.shape}")
        print(f"处理后的加速度信号形状: {A_R.shape}")
        
        # 可视化几个信号对
        plt.figure(figsize=(15, 10))
        
        # 选择前4对信号进行可视化
        num_plots = min(4, len(matched_pairs))
        
        for i in range(num_plots):
            f_col, a_col = matched_pairs[i]
            
            # 力信号
            plt.subplot(num_plots, 2, 2*i + 1)
            plt.plot(F_D[f_col])
            plt.title(f'Force Signal: {f_col}')
            plt.xlabel('Time Points')
            plt.ylabel('Force')
            plt.grid(True)
            
            # 加速度信号
            plt.subplot(num_plots, 2, 2*i + 2)
            plt.plot(A_R[a_col])
            plt.title(f'Acceleration Signal: {a_col}')
            plt.xlabel('Time Points')
            plt.ylabel('Acceleration')
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('./result/data_visualization.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 统计信息
        print(f"\n数据统计信息:")
        print(f"力信号数据范围: [{F_D.min().min():.6f}, {F_D.max().max():.6f}]")
        print(f"加速度信号数据范围: [{A_R.min().min():.6f}, {A_R.max().max():.6f}]")
        print(f"力信号数据均值: {F_D.mean().mean():.6f}")
        print(f"加速度信号数据均值: {A_R.mean().mean():.6f}")
        
        # 保存处理后的数据样本
        sample_data = {
            'force_sample': F_D.iloc[:, 0].values,
            'accel_sample': A_R.iloc[:, 0].values,
            'matched_pairs': matched_pairs[:10]
        }
        
        import pickle
        with open('./result/sample_data.pkl', 'wb') as f:
            pickle.dump(sample_data, f)
        
        print(f"\n✅ 数据加载测试成功!")
        print(f"✅ 可视化图片已保存到: ./result/data_visualization.png")
        print(f"✅ 样本数据已保存到: ./result/sample_data.pkl")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_data_loading()
