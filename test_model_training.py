#!/usr/bin/env python3
"""
测试模型训练功能
使用修改后的Excel数据加载功能进行简单的模型训练测试
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from torch.utils.data import DataLoader, Dataset
import math

def load_excel_data(excel_path):
    """从Excel文件加载2-1A1相关的力信号和加速度信号数据"""
    print("正在加载Excel数据...")
    
    # 读取力信号数据
    force_df = pd.read_excel(excel_path, sheet_name='Force_Data')
    print(f"力信号工作表形状: {force_df.shape}")
    
    # 读取加速度信号数据
    accel_df = pd.read_excel(excel_path, sheet_name='Acceleration_Data')
    print(f"加速度信号工作表形状: {accel_df.shape}")
    
    # 获取所有2-1F列（力信号）
    force_columns = [col for col in force_df.columns if col.startswith('2-1F-')]
    print(f"找到 {len(force_columns)} 个2-1F力信号列")
    
    # 获取所有2-1A1列（A1传感器的加速度信号）
    accel_columns = [col for col in accel_df.columns if col.startswith('2-1A1-')]
    print(f"找到 {len(accel_columns)} 个2-1A1加速度信号列")
    
    # 提取匹配的F和A1信号对
    matched_pairs = []
    for f_col in force_columns:
        event_num = f_col.split('-')[-1]
        a_col = f'2-1A1-{event_num}'
        if a_col in accel_columns:
            matched_pairs.append((f_col, a_col))
    
    print(f"找到 {len(matched_pairs)} 对匹配的F-A1信号")
    
    if matched_pairs:
        force_data = force_df[[pair[0] for pair in matched_pairs]]
        accel_data = accel_df[[pair[1] for pair in matched_pairs]]
        return force_data, accel_data, matched_pairs
    else:
        raise ValueError("未找到匹配的F-A1信号对")

def replace_abs_before(arr, threshold):
    """数据预处理函数"""
    for i in range(len(arr)):
        abs_arr = np.abs(arr[i])
        first_index = np.argmax(abs_arr > threshold)
        arr[i][:first_index] = 0
    return arr

def test_basic_training():
    """测试基本的模型训练流程"""
    print("开始测试模型训练...")
    
    # 1. 加载数据
    excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
    F_Load, A_Res, matched_pairs = load_excel_data(excel_path)
    
    # 2. 数据预处理
    data_length = len(F_Load)
    start_idx = max(0, data_length // 2 - 100)
    end_idx = min(data_length, start_idx + 200)
    
    F_D = F_Load.iloc[start_idx:end_idx, :]
    A_R = A_Res.iloc[start_idx:end_idx, :]
    
    print(f"数据范围: {start_idx}:{end_idx}")
    print(f"力信号形状: {F_D.shape}")
    print(f"加速度信号形状: {A_R.shape}")
    
    # 3. 转换为numpy数组并转置
    A_R = np.array(A_R).T  # 转置使每行为一个样本
    F_D = np.array(F_D).T
    
    print(f"转置后 - 加速度形状: {A_R.shape}, 力信号形状: {F_D.shape}")
    
    # 4. 数据预处理
    threshold = 0.04
    train_datas = replace_abs_before(A_R, threshold)
    Y_datas = F_D
    
    # 5. 数据划分
    num_samples = len(train_datas)
    train_size = int(0.8 * num_samples)
    
    # 随机打乱
    seed = 500
    np.random.seed(seed)
    indices = np.random.permutation(num_samples)
    
    train_indices = indices[:train_size]
    test_indices = indices[train_size:]
    
    X_train = train_datas[train_indices]
    X_test = train_datas[test_indices]
    Y_train = Y_datas[train_indices]
    Y_test = Y_datas[test_indices]
    
    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    
    # 6. 转换为PyTorch张量
    X_train = torch.tensor(X_train, dtype=torch.float32)
    X_test = torch.tensor(X_test, dtype=torch.float32)
    Y_train = torch.tensor(Y_train, dtype=torch.float32)
    Y_test = torch.tensor(Y_test, dtype=torch.float32)
    
    # 7. 数据标准化
    X_mean = X_train.mean(dim=0)
    X_std = X_train.std(dim=0)
    X_train = (X_train - X_mean) / X_std
    X_test = (X_test - X_mean) / X_std
    
    Y_mean = Y_train.mean(dim=0)
    Y_std = Y_train.std(dim=0)
    Y_train = (Y_train - Y_mean) / Y_std
    Y_test = (Y_test - Y_mean) / Y_std
    
    print("数据标准化完成")
    
    # 8. 创建简单的线性模型进行测试
    class SimpleModel(nn.Module):
        def __init__(self, input_size, hidden_size, output_size):
            super().__init__()
            self.layers = nn.Sequential(
                nn.Linear(input_size, hidden_size),
                nn.ReLU(),
                nn.Linear(hidden_size, hidden_size),
                nn.ReLU(),
                nn.Linear(hidden_size, output_size)
            )
        
        def forward(self, x):
            return self.layers(x)
    
    # 9. 初始化模型
    input_size = X_train.shape[1]
    hidden_size = 128
    output_size = Y_train.shape[1]
    
    model = SimpleModel(input_size, hidden_size, output_size)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    print(f"模型参数: 输入{input_size}, 隐藏{hidden_size}, 输出{output_size}")
    
    # 10. 简单训练
    num_epochs = 10
    batch_size = 16
    
    train_dataset = torch.utils.data.TensorDataset(X_train, Y_train)
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    
    print(f"开始训练 {num_epochs} 个epoch...")
    
    for epoch in range(num_epochs):
        model.train()
        total_loss = 0
        
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        
        avg_loss = total_loss / len(train_loader)
        print(f"Epoch [{epoch+1}/{num_epochs}], Loss: {avg_loss:.6f}")
    
    # 11. 测试
    model.eval()
    with torch.no_grad():
        test_outputs = model(X_test)
        test_loss = criterion(test_outputs, Y_test)
        print(f"测试损失: {test_loss.item():.6f}")
    
    # 12. 可视化结果
    plt.figure(figsize=(12, 8))
    
    # 选择几个样本进行可视化
    num_samples_to_plot = min(4, len(X_test))
    
    for i in range(num_samples_to_plot):
        plt.subplot(2, 2, i+1)
        
        # 反标准化
        y_true = Y_test[i].numpy() * Y_std.numpy() + Y_mean.numpy()
        y_pred = test_outputs[i].numpy() * Y_std.numpy() + Y_mean.numpy()
        
        plt.plot(y_true, 'b-', label='True', alpha=0.7)
        plt.plot(y_pred, 'r--', label='Predicted', alpha=0.7)
        plt.title(f'Sample {i+1}')
        plt.legend()
        plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('./result/training_test_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 模型训练测试完成!")
    print("✅ 结果图片已保存到: ./result/training_test_results.png")
    
    return True

if __name__ == "__main__":
    try:
        test_basic_training()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
