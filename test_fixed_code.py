#!/usr/bin/env python3
"""
测试修复后的代码
验证数据加载、预处理和模型训练是否正常工作
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd

def load_excel_data(excel_path):
    """从Excel文件加载数据"""
    print("正在加载Excel数据...")
    
    force_df = pd.read_excel(excel_path, sheet_name='Force_Data')
    accel_df = pd.read_excel(excel_path, sheet_name='Acceleration_Data')
    
    force_columns = [col for col in force_df.columns if col.startswith('2-1F-')]
    accel_columns = [col for col in accel_df.columns if col.startswith('2-1A1-')]
    
    matched_pairs = []
    for f_col in force_columns:
        event_num = f_col.split('-')[-1]
        a_col = f'2-1A1-{event_num}'
        if a_col in accel_columns:
            matched_pairs.append((f_col, a_col))
    
    if matched_pairs:
        force_data = force_df[[pair[0] for pair in matched_pairs]]
        accel_data = accel_df[[pair[1] for pair in matched_pairs]]
        return force_data, accel_data, matched_pairs
    else:
        raise ValueError("未找到匹配的F-A1信号对")

def replace_abs_before(arr, threshold):
    """修复后的数据预处理函数"""
    num_samples = arr.shape[0]  # 自动获取样本数量
    print(f"处理 {num_samples} 个样本...")
    for i in range(num_samples):
        abs_arr = np.abs(arr[i])
        first_index = np.argmax(abs_arr > threshold)
        arr[i][:first_index] = 0
    return arr

def test_data_processing():
    """测试数据处理流程"""
    print("开始测试数据处理流程...")
    
    # 1. 加载数据
    excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
    F_Load, A_Res, matched_pairs = load_excel_data(excel_path)
    
    print(f"找到 {len(matched_pairs)} 对匹配的信号")
    
    # 2. 数据范围选择
    start_idx = 0
    end_idx = 500
    
    data_length = len(F_Load)
    if end_idx > data_length:
        end_idx = data_length
        print(f"调整结束索引为: {end_idx}")
    
    F_D = F_Load.iloc[start_idx:end_idx, :]
    A_R = A_Res.iloc[start_idx:end_idx, :]
    
    print(f"数据范围: {start_idx}:{end_idx}")
    print(f"力信号形状: {F_D.shape}")
    print(f"加速度信号形状: {A_R.shape}")
    
    # 3. 转换为numpy并转置
    A_R_np = np.array(A_R).T
    F_D_np = np.array(F_D).T
    
    print(f"转置后 - 加速度形状: {A_R_np.shape}, 力信号形状: {F_D_np.shape}")
    
    # 4. 数据预处理（使用修复后的函数）
    threshold = 0.04
    train_datas_N = A_R_np
    Y_datas = F_D_np
    
    print(f"预处理前形状: {train_datas_N.shape}")
    train_datas = replace_abs_before(train_datas_N, threshold)
    print(f"预处理后形状: {train_datas.shape}")
    
    # 5. 随机打乱
    seed = 500
    np.random.seed(seed)
    np.random.shuffle(train_datas)
    np.random.seed(seed)
    np.random.shuffle(Y_datas)
    
    # 6. 划分训练集和测试集（自动计算）
    total_samples = train_datas.shape[0]
    train_ratio = 0.8
    train_size = int(total_samples * train_ratio)
    
    print(f"总样本数: {total_samples}")
    print(f"训练集大小: {train_size}")
    print(f"测试集大小: {total_samples - train_size}")
    
    X_train = train_datas[:train_size, :]
    X_test = train_datas[train_size:, :]
    Y_train = Y_datas[:train_size, :]
    Y_test = Y_datas[train_size:, :]
    
    print(f"X_train形状: {X_train.shape}")
    print(f"X_test形状: {X_test.shape}")
    print(f"Y_train形状: {Y_train.shape}")
    print(f"Y_test形状: {Y_test.shape}")
    
    # 7. 转换为PyTorch张量
    X_train = torch.tensor(X_train, dtype=torch.float32)
    X_test = torch.tensor(X_test, dtype=torch.float32)
    Y_train = torch.tensor(Y_train, dtype=torch.float32)
    Y_test = torch.tensor(Y_test, dtype=torch.float32)
    
    # 8. 数据标准化
    X_mean = X_train.mean(dim=0)
    X_std = X_train.std(dim=0)
    X_train = (X_train - X_mean) / X_std
    X_test = (X_test - X_mean) / X_std
    
    Y_mean = Y_train.mean(dim=0)
    Y_std = Y_train.std(dim=0)
    Y_train = (Y_train - Y_mean) / Y_std
    Y_test = (Y_test - Y_mean) / Y_std
    
    print("数据标准化完成")
    
    # 9. 自动配置参数
    actual_seq_length = A_R_np.shape[1]
    num_samples = A_R_np.shape[0]
    
    # 根据序列长度自动调整隐藏层维度
    if actual_seq_length < 200:
        auto_hidden_dim = 32
    elif actual_seq_length < 500:
        auto_hidden_dim = 64
    else:
        auto_hidden_dim = 128
    
    # 根据样本数量自动调整批次大小
    auto_batch_size = min(8, max(1, num_samples // 10))
    
    config = {
        'seq_length': actual_seq_length,
        'num_samples': num_samples,
        'batch_size': auto_batch_size,
        'hidden_dim': auto_hidden_dim,
        'input_dim': 1,
        'output_dim': 1,
        'num_epochs': 5,  # 测试用较少轮数
        'learning_rate': 0.0005
    }
    
    print("\n自动配置的参数:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 10. 创建简单模型测试
    class SimpleModel(nn.Module):
        def __init__(self, input_size, hidden_size, output_size):
            super().__init__()
            self.layers = nn.Sequential(
                nn.Linear(input_size, hidden_size),
                nn.ReLU(),
                nn.Linear(hidden_size, hidden_size),
                nn.ReLU(),
                nn.Linear(hidden_size, output_size)
            )
        
        def forward(self, x):
            return self.layers(x)
    
    model = SimpleModel(config['seq_length'], config['hidden_dim'], config['seq_length'])
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=config['learning_rate'])
    
    print(f"\n模型创建成功，参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 11. 简单训练测试
    print("开始训练测试...")
    train_dataset = torch.utils.data.TensorDataset(X_train, Y_train)
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
    
    for epoch in range(config['num_epochs']):
        model.train()
        total_loss = 0
        
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
        
        avg_loss = total_loss / len(train_loader)
        print(f"Epoch [{epoch+1}/{config['num_epochs']}], Loss: {avg_loss:.6f}")
    
    # 12. 测试
    model.eval()
    with torch.no_grad():
        test_outputs = model(X_test)
        test_loss = criterion(test_outputs, Y_test)
        print(f"测试损失: {test_loss.item():.6f}")
    
    print("\n✅ 所有测试通过！代码修复成功！")
    return True

if __name__ == "__main__":
    try:
        test_data_processing()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
