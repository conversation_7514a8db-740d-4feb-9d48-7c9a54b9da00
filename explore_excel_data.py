#!/usr/bin/env python3
"""
探索Excel数据结构的脚本
用于了解nn2.xlsx文件的工作表、列名、数据格式等信息
"""

import pandas as pd
import numpy as np
import os

def explore_excel_structure(excel_path):
    """探索Excel文件结构"""
    print(f"正在探索Excel文件: {excel_path}")
    print("=" * 60)
    
    try:
        # 读取Excel文件的所有工作表名称
        excel_file = pd.ExcelFile(excel_path)
        sheet_names = excel_file.sheet_names
        
        print(f"工作表数量: {len(sheet_names)}")
        print(f"工作表名称: {sheet_names}")
        print("-" * 60)
        
        # 遍历每个工作表
        for i, sheet_name in enumerate(sheet_names):
            print(f"\n工作表 {i+1}: {sheet_name}")
            print("-" * 40)
            
            # 读取工作表数据
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            
            print(f"数据形状: {df.shape}")
            print(f"列数: {len(df.columns)}")
            
            # 显示前几列的列名
            print("前10个列名:")
            for j, col in enumerate(df.columns[:10]):
                print(f"  {j+1}. {col}")
            
            if len(df.columns) > 10:
                print(f"  ... 还有 {len(df.columns) - 10} 个列")
            
            # 查找包含"2-1"的列
            cols_with_2_1 = [col for col in df.columns if '2-1' in str(col)]
            print(f"\n包含'2-1'的列数量: {len(cols_with_2_1)}")
            if cols_with_2_1:
                print("包含'2-1'的列名:")
                for col in cols_with_2_1[:10]:  # 只显示前10个
                    print(f"  - {col}")
                if len(cols_with_2_1) > 10:
                    print(f"  ... 还有 {len(cols_with_2_1) - 10} 个列")
            
            # 查找包含"2-1A1"的列
            cols_with_2_1A1 = [col for col in df.columns if '2-1A1' in str(col)]
            print(f"\n包含'2-1A1'的列数量: {len(cols_with_2_1A1)}")
            if cols_with_2_1A1:
                print("包含'2-1A1'的列名:")
                for col in cols_with_2_1A1:
                    print(f"  - {col}")
            
            # 查找包含"2-1F"的列
            cols_with_2_1F = [col for col in df.columns if '2-1F' in str(col)]
            print(f"\n包含'2-1F'的列数量: {len(cols_with_2_1F)}")
            if cols_with_2_1F:
                print("包含'2-1F'的列名:")
                for col in cols_with_2_1F:
                    print(f"  - {col}")
            
            # 显示数据的前几行
            print(f"\n前3行数据预览:")
            print(df.head(3))
            
            print("\n" + "=" * 60)
    
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")

def analyze_column_patterns(excel_path):
    """分析列名模式，特别是2-1相关的列"""
    print("\n分析列名模式...")
    print("=" * 60)
    
    try:
        excel_file = pd.ExcelFile(excel_path)
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n工作表: {sheet_name}")
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            
            # 分析所有列名
            all_columns = df.columns.tolist()
            
            # 查找不同类型的列
            force_columns = [col for col in all_columns if 'F' in str(col) and '2-1' in str(col)]
            accel_columns = [col for col in all_columns if 'A' in str(col) and '2-1' in str(col)]
            
            print(f"力信号列 (包含F和2-1): {len(force_columns)}")
            print(f"加速度信号列 (包含A和2-1): {len(accel_columns)}")
            
            # 分析2-1A1相关的列
            a1_columns = [col for col in all_columns if '2-1A1' in str(col)]
            print(f"2-1A1相关列: {len(a1_columns)}")
            for col in a1_columns:
                print(f"  - {col}")
            
            # 分析对应的F列
            f_columns = [col for col in all_columns if '2-1F' in str(col)]
            print(f"2-1F相关列: {len(f_columns)}")
            for col in f_columns:
                print(f"  - {col}")
    
    except Exception as e:
        print(f"分析列名模式时出错: {e}")

def main():
    excel_path = "/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"错误: 文件不存在 {excel_path}")
        return
    
    # 探索Excel结构
    explore_excel_structure(excel_path)
    
    # 分析列名模式
    analyze_column_patterns(excel_path)

if __name__ == "__main__":
    main()
