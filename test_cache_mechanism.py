#!/usr/bin/env python3
"""
测试缓存机制
验证数据加载缓存功能是否正常工作
"""

import pandas as pd
import numpy as np
import os
import time

def load_2_1_data_with_cache(excel_path, cache_dir='./cache'):
    """
    带缓存的2-1数据加载函数
    如果缓存文件存在则直接加载，否则从原始文件提取并缓存
    """
    # 创建缓存目录
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
        print(f"创建缓存目录: {cache_dir}")
    
    # 缓存文件路径
    cache_force_file = os.path.join(cache_dir, '2-1_force_data.xlsx')
    cache_accel_file = os.path.join(cache_dir, '2-1_accel_data.xlsx')
    
    # 检查缓存文件是否存在
    if os.path.exists(cache_force_file) and os.path.exists(cache_accel_file):
        print("发现缓存文件，直接加载...")
        start_time = time.time()
        try:
            F_data = pd.read_excel(cache_force_file)
            A_data = pd.read_excel(cache_accel_file)
            load_time = time.time() - start_time
            print(f"从缓存加载完成 - 耗时: {load_time:.2f}秒")
            print(f"力信号: {F_data.shape}, 加速度信号: {A_data.shape}")
            return F_data, A_data
        except Exception as e:
            print(f"缓存文件读取失败: {e}")
            print("将重新生成缓存文件...")
    
    # 缓存文件不存在或读取失败，从原始文件提取
    print("从原始文件提取2-1相关数据...")
    start_time = time.time()
    
    # 读取原始数据
    print("正在读取Force_Data工作表...")
    force_df = pd.read_excel(excel_path, sheet_name='Force_Data')
    print("正在读取Acceleration_Data工作表...")
    accel_df = pd.read_excel(excel_path, sheet_name='Acceleration_Data')
    
    # 提取2-1相关列
    print("提取2-1相关列...")
    force_cols = [col for col in force_df.columns if col.startswith('2-1F-')]
    accel_cols = [col for col in accel_df.columns if col.startswith('2-1A1-')]
    
    # 提取数据
    F_data = force_df[force_cols]
    A_data = accel_df[accel_cols]
    
    extract_time = time.time() - start_time
    print(f"提取完成 - 耗时: {extract_time:.2f}秒")
    print(f"力信号: {F_data.shape}, 加速度信号: {A_data.shape}")
    print(f"匹配的信号对数量: {min(len(force_cols), len(accel_cols))}")
    
    # 保存到缓存文件
    print("保存到缓存文件...")
    save_start = time.time()
    try:
        F_data.to_excel(cache_force_file, index=False)
        A_data.to_excel(cache_accel_file, index=False)
        save_time = time.time() - save_start
        print(f"缓存文件保存完成 - 耗时: {save_time:.2f}秒")
        print(f"  力信号: {cache_force_file}")
        print(f"  加速度信号: {cache_accel_file}")
    except Exception as e:
        print(f"缓存文件保存失败: {e}")
    
    return F_data, A_data

def clear_cache(cache_dir='./cache'):
    """清除缓存文件"""
    cache_files = [
        os.path.join(cache_dir, '2-1_force_data.xlsx'),
        os.path.join(cache_dir, '2-1_accel_data.xlsx')
    ]
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print(f"已删除缓存文件: {cache_file}")
    
    if os.path.exists(cache_dir) and not os.listdir(cache_dir):
        os.rmdir(cache_dir)
        print(f"已删除空缓存目录: {cache_dir}")

def test_cache_performance():
    """测试缓存性能"""
    excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
    cache_dir = './test_cache'
    
    print("=" * 60)
    print("缓存机制性能测试")
    print("=" * 60)
    
    # 1. 清除现有缓存
    print("\n1. 清除现有缓存...")
    clear_cache(cache_dir)
    
    # 2. 第一次加载（从原始文件）
    print("\n2. 第一次加载（从原始文件）...")
    start_time = time.time()
    F_data1, A_data1 = load_2_1_data_with_cache(excel_path, cache_dir)
    first_load_time = time.time() - start_time
    print(f"第一次加载总耗时: {first_load_time:.2f}秒")
    
    # 3. 第二次加载（从缓存）
    print("\n3. 第二次加载（从缓存）...")
    start_time = time.time()
    F_data2, A_data2 = load_2_1_data_with_cache(excel_path, cache_dir)
    second_load_time = time.time() - start_time
    print(f"第二次加载总耗时: {second_load_time:.2f}秒")
    
    # 4. 性能对比
    print("\n4. 性能对比:")
    speedup = first_load_time / second_load_time if second_load_time > 0 else float('inf')
    print(f"  第一次加载: {first_load_time:.2f}秒")
    print(f"  第二次加载: {second_load_time:.2f}秒")
    print(f"  加速比: {speedup:.1f}x")
    print(f"  时间节省: {first_load_time - second_load_time:.2f}秒")
    
    # 5. 数据一致性检查
    print("\n5. 数据一致性检查:")
    force_equal = F_data1.equals(F_data2)
    accel_equal = A_data1.equals(A_data2)
    print(f"  力信号数据一致: {'✅' if force_equal else '❌'}")
    print(f"  加速度信号数据一致: {'✅' if accel_equal else '❌'}")
    
    # 6. 缓存文件信息
    print("\n6. 缓存文件信息:")
    cache_force_file = os.path.join(cache_dir, '2-1_force_data.xlsx')
    cache_accel_file = os.path.join(cache_dir, '2-1_accel_data.xlsx')
    
    if os.path.exists(cache_force_file):
        force_size = os.path.getsize(cache_force_file) / (1024 * 1024)  # MB
        print(f"  力信号缓存文件: {force_size:.2f} MB")
    
    if os.path.exists(cache_accel_file):
        accel_size = os.path.getsize(cache_accel_file) / (1024 * 1024)  # MB
        print(f"  加速度信号缓存文件: {accel_size:.2f} MB")
    
    # 7. 第三次加载验证
    print("\n7. 第三次加载验证...")
    start_time = time.time()
    F_data3, A_data3 = load_2_1_data_with_cache(excel_path, cache_dir)
    third_load_time = time.time() - start_time
    print(f"第三次加载耗时: {third_load_time:.2f}秒")
    
    print("\n=" * 60)
    print("缓存机制测试完成")
    print("=" * 60)
    
    # 总结
    print(f"\n总结:")
    print(f"- 首次加载（提取+缓存）: {first_load_time:.2f}秒")
    print(f"- 缓存加载平均时间: {(second_load_time + third_load_time) / 2:.2f}秒")
    print(f"- 平均加速比: {first_load_time / ((second_load_time + third_load_time) / 2):.1f}x")
    
    if speedup > 2:
        print("✅ 缓存机制显著提升了加载速度！")
    elif speedup > 1.5:
        print("✅ 缓存机制有效提升了加载速度")
    else:
        print("⚠️ 缓存机制提升有限，可能需要优化")
    
    return True

def test_cache_functionality():
    """测试缓存功能"""
    excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
    cache_dir = './test_cache'
    
    print("测试缓存功能...")
    
    # 测试数据加载
    F_data, A_data = load_2_1_data_with_cache(excel_path, cache_dir)
    
    # 验证数据
    print(f"数据验证:")
    print(f"  力信号形状: {F_data.shape}")
    print(f"  加速度信号形状: {A_data.shape}")
    print(f"  力信号列数: {len(F_data.columns)}")
    print(f"  加速度信号列数: {len(A_data.columns)}")
    
    # 检查列名
    force_cols = [col for col in F_data.columns if col.startswith('2-1F-')]
    accel_cols = [col for col in A_data.columns if col.startswith('2-1A1-')]
    
    print(f"  2-1F列数: {len(force_cols)}")
    print(f"  2-1A1列数: {len(accel_cols)}")
    
    if len(force_cols) > 0 and len(accel_cols) > 0:
        print("✅ 数据加载功能正常")
        return True
    else:
        print("❌ 数据加载功能异常")
        return False

if __name__ == "__main__":
    try:
        # 测试基本功能
        print("1. 测试基本功能...")
        if test_cache_functionality():
            print("\n2. 测试性能...")
            test_cache_performance()
        else:
            print("基本功能测试失败，跳过性能测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
