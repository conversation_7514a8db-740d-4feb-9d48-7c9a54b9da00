#!/usr/bin/env python3
"""
测试可变测点功能
验证不同测点和传感器类型的数据加载
"""

import pandas as pd
import numpy as np
import os
import time

def load_test_point_data_with_cache(excel_path, test_point='2-1', sensor_type='A1', cache_dir='./cache'):
    """
    带缓存的测点数据加载函数
    支持不同测点和传感器类型的数据加载
    """
    # 创建缓存目录
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
        print(f"创建缓存目录: {cache_dir}")
    
    # 缓存文件路径（基于测点和传感器类型）
    cache_force_file = os.path.join(cache_dir, f'{test_point}_force_data.xlsx')
    cache_accel_file = os.path.join(cache_dir, f'{test_point}_{sensor_type}_accel_data.xlsx')
    
    # 检查缓存文件是否存在
    if os.path.exists(cache_force_file) and os.path.exists(cache_accel_file):
        print(f"发现{test_point}-{sensor_type}缓存文件，直接加载...")
        try:
            F_data = pd.read_excel(cache_force_file)
            A_data = pd.read_excel(cache_accel_file)
            print(f"从缓存加载 - 力信号: {F_data.shape}, 加速度信号: {A_data.shape}")
            return F_data, A_data
        except Exception as e:
            print(f"缓存文件读取失败: {e}")
            print("将重新生成缓存文件...")
    
    # 缓存文件不存在或读取失败，从原始文件提取
    print(f"从原始文件提取{test_point}-{sensor_type}相关数据...")
    
    # 读取原始数据
    print("正在读取Force_Data工作表...")
    force_df = pd.read_excel(excel_path, sheet_name='Force_Data')
    print("正在读取Acceleration_Data工作表...")
    accel_df = pd.read_excel(excel_path, sheet_name='Acceleration_Data')
    
    # 构造列名模式
    force_pattern = f'{test_point}F-'
    accel_pattern = f'{test_point}{sensor_type}-'
    
    # 提取相关列
    print(f"提取{test_point}-{sensor_type}相关列...")
    force_cols = [col for col in force_df.columns if col.startswith(force_pattern)]
    accel_cols = [col for col in accel_df.columns if col.startswith(accel_pattern)]
    
    print(f"找到力信号列: {len(force_cols)}个")
    print(f"找到加速度信号列: {len(accel_cols)}个")
    
    if len(force_cols) == 0:
        raise ValueError(f"未找到{test_point}的力信号数据（模式：{force_pattern}）")
    
    if len(accel_cols) == 0:
        raise ValueError(f"未找到{test_point}-{sensor_type}的加速度信号数据（模式：{accel_pattern}）")
    
    # 提取数据
    F_data = force_df[force_cols]
    A_data = accel_df[accel_cols]
    
    print(f"提取完成 - 力信号: {F_data.shape}, 加速度信号: {A_data.shape}")
    print(f"匹配的信号对数量: {min(len(force_cols), len(accel_cols))}")
    
    # 保存到缓存文件
    print("保存到缓存文件...")
    try:
        F_data.to_excel(cache_force_file, index=False)
        A_data.to_excel(cache_accel_file, index=False)
        print(f"缓存文件已保存:")
        print(f"  力信号: {cache_force_file}")
        print(f"  加速度信号: {cache_accel_file}")
    except Exception as e:
        print(f"缓存文件保存失败: {e}")
    
    return F_data, A_data

def test_different_test_points():
    """测试不同测点的数据加载"""
    excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
    cache_dir = './test_variable_cache'
    
    # 测试配置
    test_configs = [
        {'test_point': '2-1', 'sensor_type': 'A1'},
        {'test_point': '2-1', 'sensor_type': 'A2'},
        {'test_point': '2-1', 'sensor_type': 'A3'},
        {'test_point': '2-2', 'sensor_type': 'A1'},
        {'test_point': '2-3', 'sensor_type': 'A1'},
    ]
    
    print("=" * 60)
    print("测试不同测点和传感器类型的数据加载")
    print("=" * 60)
    
    results = {}
    
    for config in test_configs:
        test_point = config['test_point']
        sensor_type = config['sensor_type']
        
        print(f"\n测试配置: {test_point}-{sensor_type}")
        print("-" * 40)
        
        try:
            start_time = time.time()
            F_data, A_data = load_test_point_data_with_cache(
                excel_path=excel_path,
                test_point=test_point,
                sensor_type=sensor_type,
                cache_dir=cache_dir
            )
            load_time = time.time() - start_time
            
            results[f"{test_point}-{sensor_type}"] = {
                'success': True,
                'force_shape': F_data.shape,
                'accel_shape': A_data.shape,
                'load_time': load_time,
                'signal_pairs': min(F_data.shape[1], A_data.shape[1])
            }
            
            print(f"✅ 成功加载 {test_point}-{sensor_type}")
            print(f"   力信号: {F_data.shape}")
            print(f"   加速度信号: {A_data.shape}")
            print(f"   加载时间: {load_time:.2f}秒")
            
        except Exception as e:
            results[f"{test_point}-{sensor_type}"] = {
                'success': False,
                'error': str(e),
                'load_time': 0
            }
            
            print(f"❌ 加载失败 {test_point}-{sensor_type}: {e}")
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    successful_configs = []
    failed_configs = []
    
    for config_name, result in results.items():
        if result['success']:
            successful_configs.append(config_name)
            print(f"✅ {config_name}: {result['signal_pairs']}对信号, {result['load_time']:.2f}秒")
        else:
            failed_configs.append(config_name)
            print(f"❌ {config_name}: {result['error']}")
    
    print(f"\n成功: {len(successful_configs)}个配置")
    print(f"失败: {len(failed_configs)}个配置")
    
    # 推荐可用的配置
    if successful_configs:
        print(f"\n可用的测点配置:")
        for config in successful_configs:
            result = results[config]
            print(f"  - {config}: {result['signal_pairs']}对信号")
    
    return results

def test_cache_for_different_points():
    """测试不同测点的缓存功能"""
    excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
    cache_dir = './test_variable_cache'
    
    print("\n" + "=" * 60)
    print("测试不同测点的缓存功能")
    print("=" * 60)
    
    # 测试2-1 A1的缓存
    test_point = '2-1'
    sensor_type = 'A1'
    
    print(f"\n测试 {test_point}-{sensor_type} 的缓存功能:")
    
    # 第一次加载
    print("1. 第一次加载（生成缓存）...")
    start_time = time.time()
    F_data1, A_data1 = load_test_point_data_with_cache(
        excel_path, test_point, sensor_type, cache_dir
    )
    first_load_time = time.time() - start_time
    
    # 第二次加载
    print("\n2. 第二次加载（使用缓存）...")
    start_time = time.time()
    F_data2, A_data2 = load_test_point_data_with_cache(
        excel_path, test_point, sensor_type, cache_dir
    )
    second_load_time = time.time() - start_time
    
    # 性能对比
    speedup = first_load_time / second_load_time if second_load_time > 0 else float('inf')
    print(f"\n性能对比:")
    print(f"  第一次加载: {first_load_time:.2f}秒")
    print(f"  第二次加载: {second_load_time:.2f}秒")
    print(f"  加速比: {speedup:.1f}x")
    
    # 数据一致性检查
    force_equal = F_data1.equals(F_data2)
    accel_equal = A_data1.equals(A_data2)
    print(f"\n数据一致性:")
    print(f"  力信号数据一致: {'✅' if force_equal else '❌'}")
    print(f"  加速度信号数据一致: {'✅' if accel_equal else '❌'}")
    
    return speedup > 2

def demonstrate_config_usage():
    """演示配置使用方法"""
    print("\n" + "=" * 60)
    print("配置使用方法演示")
    print("=" * 60)
    
    # 不同的配置示例
    configs = [
        {
            'name': '2-1测点A1传感器',
            'config': {
                'test_point': '2-1',
                'sensor_type': 'A1',
                'description': '适用于2-1测点的A1传感器数据训练'
            }
        },
        {
            'name': '2-2测点A1传感器',
            'config': {
                'test_point': '2-2',
                'sensor_type': 'A1',
                'description': '适用于2-2测点的A1传感器数据训练'
            }
        },
        {
            'name': '2-1测点A2传感器',
            'config': {
                'test_point': '2-1',
                'sensor_type': 'A2',
                'description': '适用于2-1测点的A2传感器数据训练'
            }
        }
    ]
    
    print("您可以通过修改CONFIG来切换不同的测点和传感器:")
    print()
    
    for item in configs:
        print(f"# {item['name']}")
        print("CONFIG = {")
        print("    'data': {")
        print(f"        'test_point': '{item['config']['test_point']}',")
        print(f"        'sensor_type': '{item['config']['sensor_type']}',")
        print("        # 其他配置保持不变...")
        print("    }")
        print("}")
        print(f"# {item['config']['description']}")
        print()

if __name__ == "__main__":
    try:
        # 测试不同测点
        results = test_different_test_points()
        
        # 测试缓存功能
        cache_works = test_cache_for_different_points()
        
        # 演示配置使用
        demonstrate_config_usage()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        if cache_works:
            print("✅ 可变测点功能和缓存机制都工作正常")
        else:
            print("⚠️ 可变测点功能正常，但缓存性能提升有限")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
