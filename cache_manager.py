#!/usr/bin/env python3
"""
缓存管理工具
用于管理2-1数据的缓存文件
"""

import os
import pandas as pd
import time
from datetime import datetime

class CacheManager:
    """缓存管理器"""

    def __init__(self, cache_dir='./cache', test_point='2-1', sensor_type='A1'):
        self.cache_dir = cache_dir
        self.test_point = test_point
        self.sensor_type = sensor_type
        self.force_cache_file = os.path.join(cache_dir, f'{test_point}_force_data.xlsx')
        self.accel_cache_file = os.path.join(cache_dir, f'{test_point}_{sensor_type}_accel_data.xlsx')
    
    def cache_exists(self):
        """检查缓存是否存在"""
        return (os.path.exists(self.force_cache_file) and 
                os.path.exists(self.accel_cache_file))
    
    def get_cache_info(self):
        """获取缓存信息"""
        if not self.cache_exists():
            return None
        
        info = {}
        
        # 力信号缓存信息
        force_stat = os.stat(self.force_cache_file)
        info['force'] = {
            'file': self.force_cache_file,
            'size_mb': force_stat.st_size / (1024 * 1024),
            'modified': datetime.fromtimestamp(force_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 加速度信号缓存信息
        accel_stat = os.stat(self.accel_cache_file)
        info['accel'] = {
            'file': self.accel_cache_file,
            'size_mb': accel_stat.st_size / (1024 * 1024),
            'modified': datetime.fromtimestamp(accel_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return info
    
    def clear_cache(self):
        """清除缓存"""
        removed_files = []
        
        if os.path.exists(self.force_cache_file):
            os.remove(self.force_cache_file)
            removed_files.append(self.force_cache_file)
        
        if os.path.exists(self.accel_cache_file):
            os.remove(self.accel_cache_file)
            removed_files.append(self.accel_cache_file)
        
        # 如果缓存目录为空，删除目录
        if os.path.exists(self.cache_dir) and not os.listdir(self.cache_dir):
            os.rmdir(self.cache_dir)
            print(f"已删除空缓存目录: {self.cache_dir}")
        
        return removed_files
    
    def validate_cache(self):
        """验证缓存文件的完整性"""
        if not self.cache_exists():
            return False, "缓存文件不存在"
        
        try:
            # 尝试读取缓存文件
            force_df = pd.read_excel(self.force_cache_file)
            accel_df = pd.read_excel(self.accel_cache_file)
            
            # 检查数据形状
            if force_df.empty or accel_df.empty:
                return False, "缓存文件为空"
            
            # 检查列名
            force_pattern = f'{self.test_point}F-'
            accel_pattern = f'{self.test_point}{self.sensor_type}-'

            force_cols = [col for col in force_df.columns if col.startswith(force_pattern)]
            accel_cols = [col for col in accel_df.columns if col.startswith(accel_pattern)]

            if len(force_cols) == 0:
                return False, f"力信号缓存文件中没有{force_pattern}列"

            if len(accel_cols) == 0:
                return False, f"加速度信号缓存文件中没有{accel_pattern}列"
            
            return True, f"缓存有效 - 力信号: {force_df.shape}, 加速度信号: {accel_df.shape}"
            
        except Exception as e:
            return False, f"缓存文件损坏: {str(e)}"
    
    def rebuild_cache(self, excel_path):
        """重建缓存"""
        print("重建缓存...")
        
        # 清除现有缓存
        removed = self.clear_cache()
        if removed:
            print(f"已清除 {len(removed)} 个缓存文件")
        
        # 重新生成缓存
        from T_end_2_局部_copy_2_best import load_2_1_data_with_cache
        F_data, A_data = load_2_1_data_with_cache(excel_path, self.cache_dir)
        
        return F_data, A_data
    
    def print_status(self):
        """打印缓存状态"""
        print("=" * 50)
        print("缓存状态")
        print("=" * 50)
        
        if self.cache_exists():
            info = self.get_cache_info()
            print("✅ 缓存文件存在")
            print(f"缓存目录: {self.cache_dir}")
            print(f"力信号缓存: {info['force']['size_mb']:.2f} MB")
            print(f"  文件: {info['force']['file']}")
            print(f"  修改时间: {info['force']['modified']}")
            print(f"加速度信号缓存: {info['accel']['size_mb']:.2f} MB")
            print(f"  文件: {info['accel']['file']}")
            print(f"  修改时间: {info['accel']['modified']}")
            
            # 验证缓存
            is_valid, message = self.validate_cache()
            if is_valid:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
        else:
            print("❌ 缓存文件不存在")
            print(f"缓存目录: {self.cache_dir}")
        
        print("=" * 50)

def main():
    """主函数 - 缓存管理命令行工具"""
    import sys
    
    cache_manager = CacheManager()
    
    if len(sys.argv) < 2:
        print("缓存管理工具")
        print("使用方法:")
        print("  python cache_manager.py status    - 查看缓存状态")
        print("  python cache_manager.py clear     - 清除缓存")
        print("  python cache_manager.py validate  - 验证缓存")
        print("  python cache_manager.py rebuild   - 重建缓存")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'status':
        cache_manager.print_status()
    
    elif command == 'clear':
        print("清除缓存...")
        removed = cache_manager.clear_cache()
        if removed:
            print(f"已删除 {len(removed)} 个缓存文件:")
            for file in removed:
                print(f"  - {file}")
        else:
            print("没有缓存文件需要删除")
    
    elif command == 'validate':
        print("验证缓存...")
        is_valid, message = cache_manager.validate_cache()
        if is_valid:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
    
    elif command == 'rebuild':
        excel_path = '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx'
        if len(sys.argv) > 2:
            excel_path = sys.argv[2]
        
        print(f"重建缓存，数据源: {excel_path}")
        try:
            F_data, A_data = cache_manager.rebuild_cache(excel_path)
            print(f"✅ 缓存重建完成")
            print(f"力信号: {F_data.shape}, 加速度信号: {A_data.shape}")
        except Exception as e:
            print(f"❌ 缓存重建失败: {e}")
    
    else:
        print(f"未知命令: {command}")
        print("支持的命令: status, clear, validate, rebuild")

if __name__ == "__main__":
    main()
