#!/usr/bin/env python3
"""
配置示例文件
展示如何为不同测点和传感器配置训练参数
"""

# ==================== 2-1测点 A1传感器配置 ====================
CONFIG_2_1_A1 = {
    # 数据配置
    'data': {
        'excel_path': '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx',
        'cache_dir': './cache',
        'use_cache': True,
        'test_point': '2-1',     # 测点编号
        'sensor_type': 'A1',     # 传感器类型
        'start_idx': 0,
        'end_idx': 500,
        'threshold': 0.04,
        'train_ratio': 0.8,
        'random_seed': 500,
    },
    
    # 模型配置
    'model': {
        'input_dim': 1,
        'output_dim': 1,
        'hidden_dim': 64,
        'num_layers': 2,
        'window_configs': [(1,5), (2,3)],
        'max_pos_encoding': 1000,
    },
    
    # 训练配置
    'training': {
        'batch_size': 8,
        'num_epochs': 10,
        'learning_rate': 0.0005,
        'loss_alpha': 1.0,
        'loss_beta': 1.0,
    },
    
    # 输出配置
    'output': {
        'result_dir': './result',
        'model_dir': './models',
        'scaler_file': '2-1_A1_scaler_X.csv',
        'model_file': '2-1_A1_transformer_model.pth',
        'peak_errors_file': '2-1_A1_peak_errors.csv',
        'r2_scores_file': '2-1_A1_R_2.csv',
        'relative_errors_file': '2-1_A1_relative_errors.csv',
        'analysis_data_file': '2-1_A1_analysis_data.csv',
    }
}

# ==================== 2-2测点 A1传感器配置 ====================
CONFIG_2_2_A1 = {
    # 数据配置
    'data': {
        'excel_path': '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx',
        'cache_dir': './cache',
        'use_cache': True,
        'test_point': '2-2',     # 测点编号
        'sensor_type': 'A1',     # 传感器类型
        'start_idx': 0,
        'end_idx': 500,
        'threshold': 0.04,
        'train_ratio': 0.8,
        'random_seed': 500,
    },
    
    # 模型配置
    'model': {
        'input_dim': 1,
        'output_dim': 1,
        'hidden_dim': 64,
        'num_layers': 2,
        'window_configs': [(1,5), (2,3)],
        'max_pos_encoding': 1000,
    },
    
    # 训练配置
    'training': {
        'batch_size': 8,
        'num_epochs': 10,
        'learning_rate': 0.0005,
        'loss_alpha': 1.0,
        'loss_beta': 1.0,
    },
    
    # 输出配置
    'output': {
        'result_dir': './result',
        'model_dir': './models',
        'scaler_file': '2-2_A1_scaler_X.csv',
        'model_file': '2-2_A1_transformer_model.pth',
        'peak_errors_file': '2-2_A1_peak_errors.csv',
        'r2_scores_file': '2-2_A1_R_2.csv',
        'relative_errors_file': '2-2_A1_relative_errors.csv',
        'analysis_data_file': '2-2_A1_analysis_data.csv',
    }
}

# ==================== 2-1测点 A2传感器配置 ====================
CONFIG_2_1_A2 = {
    # 数据配置
    'data': {
        'excel_path': '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx',
        'cache_dir': './cache',
        'use_cache': True,
        'test_point': '2-1',     # 测点编号
        'sensor_type': 'A2',     # 传感器类型
        'start_idx': 0,
        'end_idx': 500,
        'threshold': 0.04,
        'train_ratio': 0.8,
        'random_seed': 500,
    },
    
    # 模型配置
    'model': {
        'input_dim': 1,
        'output_dim': 1,
        'hidden_dim': 64,
        'num_layers': 2,
        'window_configs': [(1,5), (2,3)],
        'max_pos_encoding': 1000,
    },
    
    # 训练配置
    'training': {
        'batch_size': 8,
        'num_epochs': 10,
        'learning_rate': 0.0005,
        'loss_alpha': 1.0,
        'loss_beta': 1.0,
    },
    
    # 输出配置
    'output': {
        'result_dir': './result',
        'model_dir': './models',
        'scaler_file': '2-1_A2_scaler_X.csv',
        'model_file': '2-1_A2_transformer_model.pth',
        'peak_errors_file': '2-1_A2_peak_errors.csv',
        'r2_scores_file': '2-1_A2_R_2.csv',
        'relative_errors_file': '2-1_A2_relative_errors.csv',
        'analysis_data_file': '2-1_A2_analysis_data.csv',
    }
}

# ==================== 使用方法 ====================
def get_config(test_point='2-1', sensor_type='A1'):
    """
    根据测点和传感器类型获取配置
    
    参数:
    test_point: 测点编号 ('2-1', '2-2', '2-3' 等)
    sensor_type: 传感器类型 ('A1', 'A2', 'A3' 等)
    
    返回:
    配置字典
    """
    config_key = f"{test_point}_{sensor_type}"
    
    if config_key == "2-1_A1":
        return CONFIG_2_1_A1
    elif config_key == "2-2_A1":
        return CONFIG_2_2_A1
    elif config_key == "2-1_A2":
        return CONFIG_2_1_A2
    else:
        # 动态生成配置
        return generate_config(test_point, sensor_type)

def generate_config(test_point, sensor_type):
    """
    动态生成配置
    """
    return {
        'data': {
            'excel_path': '/Users/<USER>/Desktop/小论文-2/论文3/data/nn2.xlsx',
            'cache_dir': './cache',
            'use_cache': True,
            'test_point': test_point,
            'sensor_type': sensor_type,
            'start_idx': 0,
            'end_idx': 500,
            'threshold': 0.04,
            'train_ratio': 0.8,
            'random_seed': 500,
        },
        
        'model': {
            'input_dim': 1,
            'output_dim': 1,
            'hidden_dim': 64,
            'num_layers': 2,
            'window_configs': [(1,5), (2,3)],
            'max_pos_encoding': 1000,
        },
        
        'training': {
            'batch_size': 8,
            'num_epochs': 10,
            'learning_rate': 0.0005,
            'loss_alpha': 1.0,
            'loss_beta': 1.0,
        },
        
        'output': {
            'result_dir': './result',
            'model_dir': './models',
            'scaler_file': f'{test_point}_{sensor_type}_scaler_X.csv',
            'model_file': f'{test_point}_{sensor_type}_transformer_model.pth',
            'peak_errors_file': f'{test_point}_{sensor_type}_peak_errors.csv',
            'r2_scores_file': f'{test_point}_{sensor_type}_R_2.csv',
            'relative_errors_file': f'{test_point}_{sensor_type}_relative_errors.csv',
            'analysis_data_file': f'{test_point}_{sensor_type}_analysis_data.csv',
        }
    }

def print_available_configs():
    """打印可用的配置"""
    print("可用的预定义配置:")
    print("- CONFIG_2_1_A1: 2-1测点 A1传感器")
    print("- CONFIG_2_2_A1: 2-2测点 A1传感器")
    print("- CONFIG_2_1_A2: 2-1测点 A2传感器")
    print()
    print("使用方法:")
    print("1. 直接使用预定义配置:")
    print("   CONFIG = CONFIG_2_1_A1")
    print()
    print("2. 动态生成配置:")
    print("   CONFIG = get_config('2-3', 'A1')")
    print()
    print("3. 在主训练文件中修改CONFIG变量:")
    print("   CONFIG['data']['test_point'] = '2-2'")
    print("   CONFIG['data']['sensor_type'] = 'A1'")

if __name__ == "__main__":
    print_available_configs()
    
    # 示例：获取不同配置
    print("\n配置示例:")
    
    configs_to_show = [
        ('2-1', 'A1'),
        ('2-2', 'A1'),
        ('2-1', 'A2'),
        ('2-3', 'A1'),
    ]
    
    for test_point, sensor_type in configs_to_show:
        config = get_config(test_point, sensor_type)
        print(f"\n{test_point}-{sensor_type}配置:")
        print(f"  测点: {config['data']['test_point']}")
        print(f"  传感器: {config['data']['sensor_type']}")
        print(f"  模型文件: {config['output']['model_file']}")
        print(f"  结果文件: {config['output']['scaler_file']}")
